<?php

class GiftForm extends Form
{
	function __construct() {

	}

	function draw() {
		global $display;
        $id = !empty(System::$data['url']['query'][3])? System::$data['url']['query'][3] : 0;
        $gift =  DB::fetch("SELECT * FROM ".T_GIFT_DETAIL." WHERE id=".$id);
        $topup_gift = explode(',',TOPUP_GIFT);
        $topup = 1;
        if(in_array($id,$topup_gift)) $topup = 2;
        if(empty($gift)){
           Url::redirect('home');
        }
        System::$data['page']['title'] = $gift['title'];

        $gift['avatar'] = MediaUrl::fromImageTitle($gift['avatar']);

        $display->add("gift",$gift);
        $display->add("id",$id);
        if($topup == 2) return $display->output("gift_topup");
		return $display->output("gift");
	}
}