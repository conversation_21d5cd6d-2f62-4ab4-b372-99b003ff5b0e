<?php

class FinishForm extends Form
{
	function __construct() {

	}

	function draw() {
		global $display;
        System::$data['page']['title'] = 'Thành công';
        $id = !empty(System::$data['url']['query'][3])? System::$data['url']['query'][3] : 0;
        $type = !empty(System::$data['url']['query'][4])? System::$data['url']['query'][4] : 0;
        $topup_gift = explode(',',TOPUP_GIFT);
        $topup = 1;
        if(in_array($id,$topup_gift)) $topup = 2;
        $display->add('type',$type);
        if($topup == 2) return $display->output("finish_topup");
		return $display->output("finish");
	}
}