<div class="page" data-name="home">
    <div class="page-content">
        <div class="home">
            <header>
                <div class="header-background" style="background: transparent url('{$gift.avatar.640}') no-repeat center center;background-size: cover">
                </div>
                <div class="header-logo">
                    <img style="width: 100%;display: inline-block;" src="{$smarty.const.WEB_ROOT}/mobile/style/hsbc/images/logo-hsbc.jpg" alt="Hsbc Reward">
                </div>
            </header>
            <main class="mT90">
                <div class="container">
                    <div class="gift-block">
                        <div class="gift-name mB10">
                            {$gift.title}
                        </div>
                        {if $gift.justGetOrder==1}
                        <div class="mB10" style="text-align: center">
                            <b>Chúc mừng quý khách hàng đã nhận được 01 thẻ hội viên Accor Plus Traveller từ HSBC, vui lòng nhập mã quà tặng và để lại thông tin nhận thẻ theo hướng dẫn dưới đây.</b><br/>
                            You have received 01 Accor Plus Traveller Membership Card from HSBC, please in put the voucher code and provide required information to receive your reward as below instructions.
                        </div>
                        {/if}
                        <div class="gift-code-input mT40">
                            <label class="mB15 pL5">Nhập mã quà tặng - Enter the giftcode</label>
                            <input onchange="_.mod.hsbc.code({$gift.id},1)" name="code" id="code" class="input-text" type="text" placeholder="Nhập mã quà - Giftcode here">
                            <input name="appName" id="appName" type="hidden" value="hsbc.reward"/>
                            <div id="errPut" class="error-message mT5 pL10 hidden">*Mã không tồn tại</div>
                        </div>
                        <input type="button" onclick="_.mod.hsbc.code({$gift.id},2)" class="btn mT125" value="Nhận quà - Confirm">
                        <div class="hotline mT20 mB20">Hotline: {HSBC_CUSTOMER_PHONE}</div>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>
