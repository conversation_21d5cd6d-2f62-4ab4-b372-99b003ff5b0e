<div class="page" data-name="receiver">
    <div class="page-content">
        <div class="home">
            <main class="">
                <div class="container">
                    <div class="gift-block">
                        <div class="gift-title mT40 mB10">
                           {* <a href="{$link}" class="btn-back external"><img src="{$smarty.const.WEB_ROOT}/website/style/grab/images/icon-back.png" alt=""></a>*}
                            <span>Nhập thông tin</span>
                        </div>
                        <div class="text-align-center mB10"><b>Please enter your information</b></div>
                        <div class="gift-desc mB10">
                            Thông tin bạn nhập sẽ được sử dụng để giao
                            sản phẩm <span>{$gift.title}</span>.
                            <br>Quý khách vui lòng nhập thông tin chính xác.
                        </div>
                        <div class="gift-desc mB10">
                            The information you enter will be used for delivering
                            <span>{$gift.title}</span>.<br>
                            Please enter the correct information.
                        </div>

                        <div class="gift-code-input mT30">
                            <label class="mB15">Số điện thoại - Phone number</label>
                            <input id="phone" name="phone" class="input-text" type="text" placeholder="Số điện thoại của bạn /Your phone number">
                            <div id="errPhone" class="error-message mT5 pL10 hidden">*Mã không tồn tại</div>
                        </div>
                        <div class="gift-code-input mT30">
                            <label class="mB15">Email</label>
                            <input id="email" name="email" class="input-text" type="text" placeholder="Email của bạn /Your email">
                            <div id="errEmail" class="error-message mT5 pL10 hidden">*Mã không tồn tại</div>
                        </div>
                        <div class="gift-code-input mT30">
                            <label class="mB15">Họ và tên - Full name</label>
                            <input id="fullname" name="fullname" class="input-text" type="text" placeholder="Tên của bạn /Your full name">
                            <div id="errFullname" class="error-message mT5 pL10 hidden">*Mã không tồn tại</div>
                        </div>
                        <div class="gift-code-input mT30">
                            <label class="mB15">Tỉnh/Thành phố - City</label>
                            <select onchange="_.mod.hsbc.district(this)" class="select-box" name="city" id="city">
                                <option value="0">Chọn - Select </option>
                                {foreach from=$city item=opt name=i}
                                    {if $opt.is_city==1}
                                    <option value="{$opt.id}">{$opt.title}</option>
                                    {/if}
                                {/foreach}
                            </select>
                            <div id="errCity" class="error-message mT5 pL10 hidden">*Mã không tồn tại</div>
                        </div>
                        <div class="gift-code-input mT30">
                            <label class="mB15">Quận/Huyện - District</label>
                            <select onchange="_.mod.hsbc.ward(this)" id="district_id" class="select-box" name="district_id">
                                <option value="0">Chọn - Select </option>
                            </select>
                            <div id="errDistrict" class="error-message mT5 pL10 hidden">*Mã không tồn tại</div>
                        </div>
                        <div class="gift-code-input mT30">
                            <label class="mB15">Phường/Xã - Ward</label>
                            <select class="select-box" name="ward" id="ward">
                                <option value="">Chọn - Select </option>
                            </select>
                            {*<input id="ward" name="ward" class="input-text" type="text" placeholder="Phường/Xã - Ward">*}
                            <div id="erWard" class="error-message mT5 pL10 hidden">*Mã không tồn tại</div>
                        </div>
                        <div class="gift-code-input mT30">
                            <label class="mB15">Địa chỉ chi tiết - Address</label>
                            <input id="address" name="address" class="input-text" type="text" placeholder="Số nhà, tên đường - Address">
                            <div id="eraddress" class="error-message mT5 pL10 hidden">*Mã không tồn tại</div>
                        </div>
                        <div class="gift-code-input mT30">
                            <label class="">Ghi chú giao hàng - Delivery notes</label>
                            <div class="mB15 label-notes">(Không bắt buộc - Optional)</div>
                            <textarea id="note" name="note" class="input-textarea" placeholder="Viết ghi chú - Write notes"></textarea>
                        </div>
                        <div class="form-notes mT15">
                            *Quà chỉ giao trong giờ hành chính (từ 9h đến 18h, thứ hai đến thứ sáu) - Gifts will only be delivered during office hours (from 9am to 6pm, Monday to Friday)<br/>
                            *Dự kiến thời gian giao hàng 5 đến 7 ngày - Estimated delivery time is 5 to 7 days
                        </div>

                        <div class="checkbox">
                            <label class="item-checkbox item-content">
                                <input onchange="_.mod.hsbc.stickYes()" id="stickVal" type="checkbox" name="demo-checkbox" checked="checked" value="1"/>
                                <i class="icon icon-checkbox"></i>
                                <div class="item-inner">
                                    <div class="item-title">Tôi tự nguyện cung cấp thông tin cho đối tác UrBox
                                        với mục đích giao quà - I voluntarily provide information to UrBox
                                        for the purpose of delivering gifts</div>
                                </div>
                            </label>
                        </div>
                        <input name="appName" id="appName" type="hidden" value="{$app.name}"/>
                        <input type="button" onclick="_.mod.hsbc.receiver({$gift.id})" class="btn btn-active mT30" value="Nhận quà - Confirm">
                        <div class="hotline mT20 mB20">Hotline: {HSBC_CUSTOMER_PHONE}</div>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>
