<?php

class ReceiverForm extends Form
{
	function __construct() {

	}

	function draw() {
		global $display;
        System::$data['page']['title'] = 'Nhập thông tin';
        $id = !empty(System::$data['url']['query'][3])? System::$data['url']['query'][3] : 0;
        $gift =  DB::fetch("SELECT * FROM ".T_GIFT_DETAIL." WHERE id=".$id);
        if(empty($gift)){
            Url::redirect('home');
        }
        $gift['avatar'] = MediaUrl::fromImageTitle($gift['avatar']);
        $topup_gift = explode(',',TOPUP_GIFT);
        $topup = 1;
        if(in_array($id,$topup_gift)) $topup = 2;
        $city = DeliveryCore::getProvince();
        $display->add("city",$city);
        $display->add("link",'/hsbc.reward/gift/code/'.$id);

        $display->add("gift",$gift);
        $display->add("id",$id);
        
        if($topup == 2)
		return $display->output("receiver_topup");
		return $display->output("receiver");
	}
}