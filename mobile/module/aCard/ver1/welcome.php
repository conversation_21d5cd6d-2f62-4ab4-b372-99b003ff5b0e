<?php
class WelcomeForm extends Form
{

    function draw() {
        global $display; $class = System::$data['namespace'];

        $token = !empty(System::$data['url']['query'][1])? System::$data['url']['query'][1] : "";
		$token = preg_replace('/[^A-Za-z0-9\-]/', '', $token);
        // lấy giftSet( để lấy ảnh + mã thẻ)
        $giftSet = BalanceVer1::getGiftCard($token);
        $display->add('token',$token);
        $display->add('giftSet',$giftSet);
        $display->add('card',System::$data['card']);
        return $display->output(System::$data['version']."welcome");
    }
}