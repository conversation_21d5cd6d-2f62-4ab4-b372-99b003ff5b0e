<div class="page" data-name="catalog">
	<div class="navbar">
		<div class="navbar-inner">
			<div class="top-head w100p">
				<div class=" w50p fl">
					<a href="javascript:void(0)" id="backlink" class="back left"><img src="{$smarty.const.WEB_ROOT}mobile/style/card/ver1/images/icon-back.svg" alt=""> Quay lại</a>
				</div>
				<div class="my-point w50p fr text-align-right">
					<span class="f-300 f12">Số dư</span> <span class="color-spruce f14 f-bold">{$card.money|number_format}{VIEW_CURRENCY}</span>
				</div>
			</div>
			<div class="head-tab w100p absolute bottom-0 text-align-center">
				<a href="javascript:void(0)" onclick="_.click.link('/card/{$card.token}')" class="w50p fl f14 active">Đổi voucher</a>
				<a href="javascript:void(0)" onclick="_.click.link('/card/{$card.token}?page=account')" class="w50p fr f14">Voucher của tôi</a>
			</div>
		</div>
	</div>
	<div class="page-content infinite-scroll-content"  data-infinite-distance='500'>
		<div class="page-content-inner">
		<div class="catalog_filter over-hidden mT10 pL15 pR15">
			<div class="catalog_type fl w50p">
				<label class="f12 f-300" for="id_danhmuc">Danh mục</label>
				<select name="" class="f14 pointer" id="id_danhmuc" onchange="_.click.link('/card/{$token}?page=catalog&id='+this.value)">
					<option value="0">Tất cả</option>
					{foreach from=$category item=cat name=i}
						<option value="{$cat.id}" {if $cat_id==$cat.id} selected {/if}>{$cat.title}</option>
					{/foreach}
				</select>
			</div>
			<div class="catalog_location border-box fr w50p pL20">
				<div id="find-location" onclick="_.click.findLocation(0)">
					<h5 class="f12 f-300">Địa điểm</h5>
					<div id="my-location" class="f14 f-mbold fl over-hidden my-location">{$city.title}</div>
				</div>
			</div>
		</div>

		<div class="container">
			<div class="mT30"></div>
			<form class="searchbar hidden-search ">
				<div class="searchbar-inner">
					<div class="searchbar-input-wrap">
						<input type="search" placeholder="Tìm thương hiệu"/>
						<i class="searchbar-icon"></i>
						<span class="input-clear-button"></span>
					</div>
					<span class="searchbar-disable-button if-not-aurora">Ẩn</span>
				</div>
			</form>
			<div class="searchbar-backdrop"></div>
			<div class="list searchbar-found hidden">
				<div class="block-brand">
					<ul class="brand-list" id="brand-result"></ul>
				</div>
			</div>
			<div class="searchbar-not-found no-border">
				<div class="block-inner f16 f-300">UrBox chưa có cửa hàng quanh đây.</div>
			</div>
			<div class="searchbar-hide-on-search mT30">
				<input type="hidden" value="1" id="js_page_no" name="js_page_no"/>
				<input type="hidden" value="{$per_page}" id="js_per_page" name="js_per_page"/>
				<input type="hidden" value="{$cat_id}" id="js_cat_id" name="js_cat_id"/>
				<input type="hidden" value="{$card.token}" id="js_token" name="js_token"/>
				<input type="hidden" value="{$view_type}" id="js_view_type" name="js_view_type"/>
				<input type="hidden" value="catalog" id="js_page_name" name="js_page_name"/>
				{if $view_type == "list"}
					<div class="list media-list">
					<ul id="jsBlockBrand">
						{foreach from = $all_brands item = $brand}
							<li class="item-content list-office pointer" onclick="_.click.link('/card/{$token}?page=brand&id={$brand.id}')">
								<div class="item-media brand-img">
									<img src="{$brand.image}" alt="">
								</div>
								<div class="item-inner no-border">
									<div class="items-content w100p">
										<div class="item-title f16 f-500 display-block"><span class='item-name'>{$brand.title}</span> <span class="color-4a f-300 f14 fr">
												{if $brand.distance !== "-"}
													{$brand.distance} km
												{/if}
											</span></div>
										<div class="item-subtitle color-4a f14 f-300">{$brand.address}</div>
									</div>
								</div>
							</li>
						{/foreach}
					</ul>
					</div>
				{else}
					<div class="block-brand">
						<ul id="jsBlockBrand"  class="brand-list">
							{foreach from = $all_brands item = $brand}
							<li>
								<div class="pointer" onclick="_.click.link('/card/{$token}?page=brand&id={$brand.id}')">
									<img src="{$brand.image}" alt="{$brand.title}">
									<div class="item-title hidden">{$brand.title}</div>
								</div>
							</li>
							{/foreach}
						</ul>
					</div>
				{/if}
			</div>

			{*{if $view_type == "list"}
			<a href="/card/{$token}?page=catalog&id={$cat_id}&view_type=grid" id="change-view">
				<img src="{$smarty.const.WEB_ROOT}mobile/style/card/ver1/images/icon-grid.svg" alt="">
			</a>
			{else}
			<a href="/card/{$token}?page=catalog&id={$cat_id}&view_type=list" id="change-view" class="view-list">
				<img src="{$smarty.const.WEB_ROOT}mobile/style/card/ver1/images/icon-list.svg" alt="">
			</a>
			{/if}*}
		</div>
		</div>
	</div>
</div>
<script>
	let token = '{$token}';
</script>