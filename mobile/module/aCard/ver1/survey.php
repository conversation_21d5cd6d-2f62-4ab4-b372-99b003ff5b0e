<?php

class SurveyForm extends Form
{
    public function __construct()
    {
    }

    public function draw()
    {
        global $display; $class = System::$data['namespace'];
        $token = !empty(System::$data['url']['query'][1])? System::$data['url']['query'][1] : "";
		$token = preg_replace('/[^A-Za-z0-9\-]/', '', $token);
        $survey_id = Balance::checkSurvey($token);
        if ($survey_id == false || $survey_id <= 0){
            Url::redirect('/card/' . $token);
        }
        $card = Card::getByToken($token);
        $app = Balance::getApp($card);
        $survey = Balance::getSurvey($survey_id);
        $display->add('survey',$survey);
        $display->add('card',$card);
        $display->add('app',$app);
        $display->add('token',$token);
        return $display->output(System::$data['version'].'survey');
    }
}