<?php
class BrandForm extends Form
{

    function draw() {
        global $display; $class = System::$data['namespace'];

        #Danh muc
        $brand_id = Url::getParamInt('id',"");
        $per_page = Url::getParamInt('per_page', 10);
        $token = !empty(System::$data['url']['query'][1])? System::$data['url']['query'][1] : "";
		$token = preg_replace('/[^A-Za-z0-9\-]/', '', $token);
		$tab = Url::getParamInt('tab',0);
        if($brand_id < 0) Url::redirect("card/" . $token);

        // lưu brand vào danh sách đã xem
        BalanceVer1::setBrandToCache($brand_id);

        //lấy trạng thái cho phép truy cập vị trí hay khôing
        $allow_loc = BalanceVer1::getAccessLoc();

        //lấy thông tin brand
        $brand = BalanceVer1::getBrandById($brand_id,$token);
        if(empty($brand)){
            BalanceVer1::removeCacheBrand($brand_id);
            Url::redirect("card/" . $token);
        }
        // lấy giftSet( để lấy ảnh + mã thẻ)


        // lấy danh sách gift detail hiển thị ra trang chủ
        $data = BalanceVer1::getGiftByBrand($token,$brand_id);

        $city = BalanceVer1::getCityName();
        $display->add('allow_loc',$allow_loc);
        $display->add('brand',$brand);
        $display->add('city',$city);
        $display->add('brand_id',$brand_id);
        $display->add('token',$token);
        $display->add('data',$data);
        $display->add('tab',$tab);
        $display->add('per_page',$per_page);
        $display->add('card',System::$data['card']);

        if(!empty($brand_id))
            return $display->output(System::$data['version']."browseByBrand");
        return $display->output(System::$data['version']."browse");
    }
}