<?php

class AccountForm extends Form
{

    function draw() {
        global $display; $class = System::$data['namespace'];

        $card_id = System::$data['card']['id'];
        $token = !empty(System::$data['url']['query'][1])? System::$data['url']['query'][1] : "";
		$token = preg_replace('/[^A-Za-z0-9\-]/', '', $token);
        $gift_used = BalanceVer1::usedVoucher($card_id);
        $data = BalanceVer1::myVoucher($card_id,1);
        //$gift_expired['brand'] = [];
        //$gift_expiring['brand'] = [];
        $gift_expiring = BalanceVer1::myVoucher($card_id,2);
        $gift_expired = BalanceVer1::myVoucher($card_id,3);
        $display->add('gift_used',$gift_used);
        $display->add('today',TIME_NOW);
        $display->add('token',$token);
        $display->add('data',$data);
        $display->add('gift_expiring',$gift_expiring);
        $display->add('gift_expired',$gift_expired);
        $display->add('card',System::$data['card']);
        return $display->output(System::$data['version']."account");

    }
}