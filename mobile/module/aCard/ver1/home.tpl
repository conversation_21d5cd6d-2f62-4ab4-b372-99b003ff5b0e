<div class="page" data-name="home">
    <div class="navbar">
        <div class="navbar-inner">
            <div class="top-head w100p">
                <div class="ur-logo w50p fl">
                    <a href="javascript:void(0)">
                        {if $app.logo!=''}
                            <img src="{$app.logo}" height="35">
                        {else}
                        <img src="{$smarty.const.WEB_ROOT}mobile/style/card/ver1/images/logo-urbox.svg" alt="">
                        {/if}
                    </a>
                </div>
                <div class="my-point w50p fr text-align-right">
                    <span class="f-300 f12">Số dư</span> <span class="color-spruce f14 f-bold">{$card.money|number_format}{VIEW_CURRENCY}</span>
                </div>
            </div>
            <div class="head-tab w100p absolute bottom-0 text-align-center">
                <a href="javascript:void(0)" class="w50p fl f14 active">Đổi voucher</a>
                <a href="javascript:void(0)" onclick="_.click.link('/card/{$card.token}?page=account')" class="w50p fr f14">Voucher của tôi</a>
            </div>
        </div>
    </div>
    <div class="page-content">
        <div class="page-content-inner">
        <div class="container">
            <div class="block-card">
                <div class="card-bank mT40" data-card="{$card.number}" onclick="_.mod.browse.showNumberCard(this)">
                    {if !empty($app.image)}
                        <img src="{$app.image}" class="w100p" alt="">
                    {else}
                        <img src="{$smarty.const.WEB_ROOT}mobile/style/card/ver1/images/bg-card.png" class="w100p" alt="">
                    {/if}
                    <div class="card-number copy-card">
                        **** **** **** {$card.number|substr:12:4}
                    </div>
                </div>
            </div>
            <input type="text" id="cardNumber" value="{$card.number}" style="position: absolute;border: 0;text-indent: -9999px; height: 0; width: 100%;" />
            <div class="mT30"></div>
            <form class="searchbar hidden-search">
                <div class="searchbar-inner">
                    <div class="searchbar-input-wrap">
                        <input type="search" placeholder="Tìm thương hiệu">
                        <i class="searchbar-icon"></i>
                        <span class="input-clear-button"></span>
                    </div>
                    <span class="searchbar-disable-button if-not-aurora">Hủy</span>
                </div>
            </form>
            <div class="searchbar-backdrop"></div>

            <div class="list searchbar-found list-border hidden">
                <ul id="brand-result">

                </ul>
                {*danh sach brand se hien thi o day*}
            </div>
            <div class="searchbar-not-found">
                <div class="block-inner f16 f-300">Không tìm thấy thương hiệu</div>
            </div>

            <div class="searchbar-hide-on-search">
                <div class="clear_both"></div>
                {if $allowBrand}
                    <div class="pB20 pT20">
                        <a href="javascript:void(0)" onclick="_.click.link('/card/{$token}?page=brand&id=421')"><img class="w100p" src="/mobile/style/card/ver3/images/banner-pti.jpg" alt=""></a>
                    </div>
                {/if}
                <div class="block-filter">
                    <h4 class="title f16">Danh mục</h4>
                    <div class="swiper-container swiper-init filter-list" data-space-between="20" data-slides-per-view="auto">
                        <div class="swiper-wrapper">
                            <div class="swiper-slide">
                                <div class="pointer" onclick="_.click.link('/card/{$token}?page=catalog&id=0')">
                                    <div><img src="{$smarty.const.WEB_ROOT}/mobile/style/card/ver1/images/filter-all.svg" alt=""></div>
                                    <span>Tất cả</span>
                                </div>
                            </div>
                            {foreach from=$category item=cat name=i}
                                <div class="swiper-slide">
                                    <div class="pointer" onclick="_.click.link('/card/{$token}?page=catalog&id={$cat.id}')">
                                        <div><img src="{$cat.image}" alt=""></div>
                                        <span>{$cat.title}</span>
                                    </div>
                                </div>
                            {/foreach}
                        </div>
                    </div>
                </div>
                <input type="hidden" value="1" id="js_page_no" name="js_page_no"/>
                <input type="hidden" value="{$per_page}" id="js_per_page" name="js_per_page"/>
                <input type="hidden" value="0" id="js_cat_id" name="js_cat_id"/>
                <input type="hidden" value="{$card.token}" id="js_token" name="js_token"/>
                <input type="hidden" value="home" id="js_page_name" name="js_page_name"/>

                {*danh sách thương hiệu đã xem*}
               {if  $viewed_brand != null}
                <div class="block-brand mT20">
                    <h4 class="title f16">Thương hiệu đã xem</h4>
                    <ul class="brand-list">
                        {foreach from = $viewed_brand item = $brand}
                        <li>
                            <div class="pointer" onclick="_.click.link('/card/{$token}?page=brand&id={$brand.id}')">
                                <img src="{$brand.image}" alt="{$brand.title}">
                            </div>
                        </li>
                        {/foreach}
                    </ul>
                </div>
                {/if}
                <a class="view-all-brand mT20 pointer" href="javascript:void(0)" onclick="_.click.link('/card/{$token}?page=catalog&id=0&view_type=grid')">Xem tất cả thương hiệu</a>
                <div class="mT20">
                    <h4 class="title">Thương hiệu nổi bật</h4>
                    {if $data}
                    <div class="list media-list">
                        <ul class="brand-list">
                            {foreach from=$data item=$brand}
                                <li>
                                    <div class="pointer" onclick="_.click.link('/card/{$token}?page=brand&id={$brand.id}')">
                                        <img src="{$brand.image}" alt="{$brand.title}">
                                    </div>
                                </li>
                            {/foreach}
                        </ul>

                    </div>
                    {else}
                        <h3 class="mT15 mB20">Sản phẩm đang được cập nhật.</h3>
                    {/if}
                </div>
            </div>
        </div>
        </div>
    </div>
</div>
<script>
    let token = '{$token}';
</script>