<?php
class SendForm extends Form
{

    function draw() {
        global $display; $class = System::$data['namespace'];

		$id = Url::getParamInt('id',0);
        $cart = DB::fetch('SELECT * FROM '.T_CART.' WHERE id='.$id);
        $cartLink = '';
        if($cart){
            $re = DB::query("SELECT * FROM ".T_CART_DETAIL." WHERE cart_id=".$cart['id']);
            if($re){
                $cart_detail = array();
                $arr_gift_id = array();
                $arr_gift_detail_id = array();
                while ($row = $re->fetch(PDO::FETCH_ASSOC)){
                    $cart_detail = $row;
                    $arr_gift_id[$row['gift_id']] = $row['gift_id'];
                    $arr_gift_detail_id[$row['gift_detail_id']] = $row['gift_detail_id'];
                    $cartLink = LINK_URBOX.'nhan-qua/'.System::decrypt($row['receive_code']).'.html';
                }
                $cart['detail'] = $cart_detail;
                if(!empty($arr_gift_detail_id)){
                    $re = DB::query("SELECT * FROM ".T_GIFT_DETAIL." WHERE id IN(".implode(',',$arr_gift_detail_id).")");
                    if($re){
                        $gift_detail = array();
                        while ($row = $re->fetch(PDO::FETCH_ASSOC)){
                            $images = MediaUrl::fromImageTitle($row['avatar']);
                            $row['image'] = isset($images[640])?$images[640]:'';
                            $row['brand'] = DB::fetch("SELECT title FROM ".T_BRAND." WHERE id=".$row['brand_id']);
                            $gift_detail = $row;

                        }

                        $cart['gift'] = $gift_detail;
                    }
                }
            }
        }
        $display->add('token',"");
        $display->add('cart',$cart);
        $display->add('cartLink',$cartLink);
        $display->add('card',System::$data['card']);
        $rec = DB::fetch_all("SELECT id FROM ".T_CART." WHERE status=".IS_ON." AND pay_status=".IS_YES." AND card_id=".System::$data['card']['id']);
        $numOrder = 0;
        if($rec){
            $numOrder = count($rec);
        }
        $display->add('numOrder',$numOrder);
        return $display->output(System::$data['version']."send");
    }
}