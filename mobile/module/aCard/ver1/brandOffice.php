<?php
class BrandOfficeForm extends Form
{

    function draw() {
        global $display; $class = System::$data['namespace'];

        #Danh muc
        $brand_id = Url::getParamInt('id',"");
        $gift_id = Url::getParamInt('gift_id',"");
        $per_page = Url::getParamInt('per_page', 10);
        $token = !empty(System::$data['url']['query'][1])? System::$data['url']['query'][1] : "";
		$token = preg_replace('/[^A-Za-z0-9\-]/', '', $token);
		$tab = Url::getParamInt('tab',0);
        if($brand_id < 0) Url::redirect("/card/" . $token);

        // lưu brand vào danh sách đã xem
        BalanceVer1::setBrandToCache($brand_id);

        //lấy trạng thái cho phép truy cập vị trí hay khôing
        $allow_loc = BalanceVer1::getAccessLoc();

        //lấy thông tin brand
        $brand = BalanceVer1::getBrandById($brand_id,$token,$gift_id);
        // lấy giftSet( để lấy ảnh + mã thẻ)

        $city = BalanceVer1::getCityName();
        $display->add('allow_loc',$allow_loc);
        $display->add('brand',$brand);
        $display->add('city',$city);
        $display->add('app',System::$data['apps']);
        $display->add('brand_id',$brand_id);
        $display->add('token',$token);
        $display->add('tab',$tab);
        $display->add('per_page',$per_page);
        $display->add('card',System::$data['card']);

        return $display->output(System::$data['version']."brandOffice");
    }
}