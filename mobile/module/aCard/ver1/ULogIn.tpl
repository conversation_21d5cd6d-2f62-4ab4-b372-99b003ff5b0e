<div class="page" data-name="ulogin">
    <div class="navbar">
        <div class="navbar-inner">
            <div class="top-head w100p text-align-center pT5">
                <div class="ur-logo">
                    <a href="javascript:void(0)">
                        {if !empty($app.logo)}
                            <img src="{$app.logo}" alt="" class="h60 icons">
                        {else}
                            <img src="{$smarty.const.WEB_ROOT}mobile/style/card/images/logo-urbox.svg" class="h60 icons" alt="">
                        {/if}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="page-content">
        <div class="page-content-inner">
        <div class="container">
            <div class="block-card">
                <div class="card-bank mT40" data-card="{$card.number}">
                    {if !empty($app.image)}
                        <img src="{$app.image}" class="w100p" alt="">
                    {else}
                        <img src="{$smarty.const.WEB_ROOT}mobile/style/card/ver1/images/bg-card.png" class="w100p" alt="">
                    {/if}
                    <div class="card-number">
                        **** **** **** {$card.number|substr:12:4}
                    </div>
                </div>
                <div class="card-amount">
                    <span class="amount-text">Số dư</span>
                    <span class="amount-num">{$card.money|number_format} {VIEW_CURRENCY}</span>
                </div>
            </div>

			<div class="mT25 f15 w100p f-bold text-center center-content lh">
                Nhập mã PIN được cấp để kích hoạt thẻ
			</div>
			<div class="form-input mT15 mB50">
                <div style="position: relative">
				    <input maxlength="7" type="password" class="" id="input-ulogin" placeholder="Mã PIN">
                    <span toggle="#input-ulogin" class="toggle-password fa-eye"></span>
                </div>
				<div class="error-message hidden" id="message-login">Mã PIN không đúng</div>
			</div>
            <div class="w100p mT50 mB10">
                <a href="javascript: void(0)" onclick="_.mod.onSetupPin.checkUPin('{$token}')" class="btn btn-active w100p" >Truy cập</a>
            </div>
            <a target="_parent" href="tel:{UB_CUSTOMER_PHONE_VALUE}" class="hotlineNum external">Hotline: <span>{UB_CUSTOMER_PHONE}</span></a>
		</div>
        </div>
	</div>
</div>

<script>
    let token = '{$token}';
</script>