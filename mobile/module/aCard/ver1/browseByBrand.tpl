<div class="page" data-name="brand">
	<div class="navbar">
		<div class="navbar-inner">
			<div class="top-head w100p">
				<div class=" w50p fl">
					<a href="javascript:void(0)" id="backlink" class="back left"><img src="{$smarty.const.WEB_ROOT}mobile/style/card/ver1/images/icon-back.svg" alt=""> Quay lại</a>
					{*<a href="javascript:void(0)" onclick="_.click.link('/card/{$card.token}')"><img src="{$smarty.const.WEB_ROOT}mobile/style/card/ver1/images/logo-urbox.svg" alt=""></a>*}
				</div>
				<div class="my-point w50p fr text-align-right">
					<span class="f-300 f12">Số dư</span> <span class="color-spruce f14 f-bold">{$card.money|number_format}{VIEW_CURRENCY}</span>
				</div>
			</div>
			<div class="head-tab w100p absolute bottom-0 text-align-center">
				<a href="javascript:void(0)" onclick="_.click.link('/card/{$card.token}')" class="w50p fl f14 active">Đổi voucher</a>
				<a href="javascript:void(0)" onclick="_.click.link('/card/{$card.token}?page=account')" class="w50p fr f14">Voucher của tôi</a>
			</div>
		</div>
	</div>
	<div class="page-content">
		<div class="page-content-inner">
		<div class="container">
			<div class="brand_top mT10 over-hidden">
				<div class="brand_top_img fl">
					<img src="{$brand.image}" alt="">
				</div>
				<div class="brand_top_content over-hidden pR10 pL10 fl">
					<div class="brand_top_title mT15">
						<span class="f-bold f16">{$brand.title}</span>
						<div class="brand_top_like hidden">
							<span class="f12 f-300">5 lượt yêu thích</span>
						</div>
					</div>
				</div>
				<div class="brand_top_like_btn mT20 fr hidden">
					<a class="btn-brand-like f14 f-300 color-spruce" href="javascript: void(0)">Yêu thích</a>
				</div>
			</div>
		</div>
		<div class="mT10 mB20 over-hidden brand-tab">
			<div class="w50p brand-buy-voucher text-align-center fl">
				<a href="#brand-voucher" class="tab-link {if $tab !=1}tab-link-active{/if}"><span class="f14">Mua voucher</span></a>
			</div>
			<div class="w50p brand-address text-align-center fr">
				<a href="#brand-address" class="tab-link {if $tab ==1}tab-link-active{/if}"><span class="f14">Địa chỉ</span></a>
			</div>
		</div>
		<div class="container">
			<div class="hidden swiper-container swiper-init" data-slides-per-view="auto">
				<div class="swiper-wrapper title-catalog">
					<a href="/brand" class="swiper-slide">Voucher mệnh giá</a>
					<a href="/brand-product" class="swiper-slide swiper-slide-active">Sản phẩm</a>
					<a href="/brand-discount" class="swiper-slide">Voucher giảm giá</a>
				</div>
			</div>
			<input type="hidden" value="1" id="js_total_List" name="js_total_List"/>
			<input type="hidden" value="{$per_page}" id="js_per_page" name="js_per_page"/>
			<input type="hidden" value="{$brand_id}" id="js_brand_id" name="js_brand_id"/>
			<input type="hidden" value="{$card.token}" id="js_token" name="js_token"/>
			<div class="tabs">
				{*tab 1*}
				<div class="tab {if $tab !=1}tab-active{/if}" id="brand-voucher">
					{if $data}
						<div class="list media-list">
							<ul id="jsBlockGift">
								{foreach from=$data item=item name=i}
									<li>
										<div onclick="_.click.detail('{$item.id}')" class="item-link item-content">
											<div class="item-media"><img src="{$item.image}" alt="{$item.title}"></div>
											<div class="item-inner">
												<div class="item-title">{$item.title}</div>
												<div class="item-subtitle color-spruce f14 f-bold">{$item.price|number_format:0:',':'.'}{VIEW_CURRENCY}</div>
												<div class="item-subtitle color-spruce added-class f12 f-300" id="added-{$item.id}"></div>
											</div>
										</div>
									</li>
								{/foreach}
							</ul>

						</div>
					{else}
						<h5 class="mT15 mB20">Thương hiệu này hiện không có voucher nào áp dụng được ở tỉnh thành bạn đang chọn.</h5>
					{/if}
				</div>
				{*hết tab 1*}
				{*tab 2*}
				<div class="tab {if $tab ==1}tab-active{/if}" id="brand-address">
					<div class="title display-flex flex-sb align-items-center">
						<div class="w100p">

							<div class="catalog_location border-box w100p" style="border-left: 0">
								<div id="find-location" onclick="_.click.findLocation(1)">
									<h5 class="f12 f-300 pT0 pB0 mT0 mB0">Địa điểm</h5>
									<div id="my-location" class="f14 f-mbold fl over-hidden my-location">{$city.title}</div>
								</div>
							</div>
						</div>
					</div>
					<div class="list list-address searchbar-found">
						<ul>
							{foreach from =$brand.office item=$address}
							<li class="item-content list-office"  {if $address.show != 1}style="display: none;"{/if}>
								<div class="brand-img item-media">
									<img src="{$brand.image}" alt="">
								</div>
								<div class="item-inner no-border">
									<div class="items-content w100p">
										<div class="item-title f-500 display-block">{$brand.title} <span class="color-4a f-300 f13 fr">
												{if $address.distance !== "-"}
													{$address.distance} km
												{/if}
											</span></div>
										<div class="item-subtitle color-4a f14 f-300">{$address.address}</div>
									</div>
								</div>
							</li>
							{/foreach}
						</ul>
					</div>
				</div>
				{*hết tab 2*}
			</div>
			<div class="gift-dialogs popup">
				<div class="dialog-inner pT0">
					<div class="dialog-text" id="gift-detail-content">

					</div>
				</div>
			</div>
			<div class="cart-dialogs popup">
				<div class="dialog-inner">
					<div class="dialog-title text-align-left">
						Giỏ hàng
					</div>
					<div class="dialog-text" id="cart-detail-content">

					</div>
				</div>
			</div>
			<div class="cart" id="my_cart" onclick="_.click.viewCart()">
				<span class="f16 f-bold color-white">Giỏ hàng
				<span class="f16 f-300 mL15 color-white" id="cart_total_gift">0 quà</span>
				</span>
				<span class="f16 f-bold color-white" id="cart_total_price">0đ</span>
			</div>

		</div>
		</div>
	</div>
</div>
<script>
	let token = '{$token}';
</script>