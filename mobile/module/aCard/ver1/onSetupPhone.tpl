<div class="page" data-name="account">
    <div class="navbar">
        <div class="navbar-inner">
            <div class="top-head w100p text-align-center pT5">
                <div class="ur-logo">
                    <a href="javascript:void(0)">
                        {if !empty($app.logo)}
                            <img src="{$app.logo}" alt="" class="h60 icons">
                        {else}
                            <img src="{$smarty.const.WEB_ROOT}mobile/style/card/ver1/images/logo-urbox.svg" class="h60 icons" alt="">
                        {/if}
                    </a>
                </div>
            </div>
        </div>
    </div>
    <div class="toolbar toolbar-bottom">
        <div class="toolbar-inner">
            <div class="mB10 w100p">
                <a href="javascript:void(0)" onclick="_.mod.onSetupPhone.showOtp('{$token}')" class="btn btn-active w100p"><PERSON><PERSON><PERSON> ho<PERSON>t</a>
            </div>
            <a target="_parent" href="tel:{UB_CUSTOMER_PHONE_VALUE}" class="hotlineNum external">Hotline: <span>{UB_CUSTOMER_PHONE}</span></a>
        </div>
    </div>
    <div class="page-content">
        <div class="page-content-inner">
        <div class="container">
            <div class="block-card">
                <div class="card-bank mT40" data-card="{$card.number}" onclick="_.mod.browse.showNumberCard(this)">
                    {if !empty($app.image)}
                        <img src="{$app.image}" class="w100p" alt="">
                    {else}
                        <img src="{$smarty.const.WEB_ROOT}mobile/style/card/ver1/images/bg-card.png" class="w100p" alt="">
                    {/if}
                    <div class="card-number">
                        **** **** **** {$card.number|substr:12:4}
                    </div>
                </div>
                <div class="card-amount">
                    <span class="amount-text">Số dư</span>
                    <span class="amount-num">{$card.money|number_format} {VIEW_CURRENCY}</span>
                </div>
            </div>

            <div class="mT25 f18 w60p f-bold text-center center-content lh">
                Đổi quà trong hạn mức số dư còn lại
            </div>
            <div class="form-input mT15 mB15">
                <label class="f-bold mB15 f15 text-align-left" for="">Nhập số điện thoại để kích hoạt gói quà tặng</label>
                <input type="text" class="" id="input-phone" placeholder="Số điện thoại của bạn">
                <div class="error-message hidden" id="phone_error">Số điện thoại không chính xác!</div>
            </div>
            {if $useIdentity == 2}
            <div class="form-input mT15 mB15">
                <label class="f-bold mB15 f15 text-align-left" for="">Nhập số chứng minh thư/hộ chiếu</label>
                <input type="text" class="" id="input-identity" placeholder="Số chứng minh thư/hộ chiếu của bạn">
                <div class="error-message hidden" id="input-identity">Số chứng minh thư/hộ chiếu không chính xác!</div>
            </div>
            {else}
                <input type="hidden" class="" id="input-identity">
            {/if}

        </div>
        </div>
    </div>
</div>
<script>
    let token = '{$token}';
</script>