<?php
class CatalogForm extends Form
{

    function draw() {
        global $display; $class = System::$data['namespace'];

        #Danh muc
        $parent_id = Url::getParam('parent_id',CGlobal::$cateIdProduct);
        $per_page = Url::getParamInt('per_page', 18);
        $token = !empty(System::$data['url']['query'][1])? System::$data['url']['query'][1] : "";
		$token = preg_replace('/[^A-Za-z0-9\-]/', '', $token);
		$cat_id = Url::getParamInt('id',0);
        $allow_loc = BalanceVer1::getAccessLoc();
        if($allow_loc == 0){
            $default_view = "grid";
        }else $default_view = 'list';
		$view_type = Url::getParam('view_type',$default_view);
        // lấy danh sách category
        $category = BalanceVer1::getCategory($parent_id);
        if($view_type == 'grid'){
            $group_by= true;
        }else $group_by= false;
        $all_brands = BalanceVer1::listBrands($cat_id,true,$token,$per_page,1,$group_by);
        //lấy trạng thái cho phép truy cập vị trí hay khôing


        // lấy danh sách gift detail hiển thị ra trang chủ

        $giftSet = BalanceVer1::getGiftCard($token);
        $city = BalanceVer1::getCityName();
        $display->add('category',$category);
        $display->add('city',$city);
        $display->add('allow_loc',$allow_loc);
        $display->add('all_brands',$all_brands);
        $display->add('token',$token);
        $display->add('per_page',$per_page);
        $display->add('cat_id',$cat_id);
        $display->add('view_type',$view_type);
        $display->add('card',System::$data['card']);
        $display->add('giftSet',$giftSet);
        return $display->output(System::$data['version']."catalog");
    }
}