<div class="page" data-name="welcome">
	<div class="navbar">
		<div class="navbar-inner">
			<div class="top-head w100p text-align-center">
				<div class="ur-logo">
					<a href="javascript:void(0)" id="backlink" data-force="false" class="back left fl"><img src="{$smarty.const.WEB_ROOT}mobile/style/card/ver1/images/icon-back.svg" alt=""> Quay lại</a>
					<a href="/card/{$card.token}"><img src="{$smarty.const.WEB_ROOT}mobile/style/card/ver1/images/logo-urbox.svg" alt=""></a>
				</div>
			</div>
		</div>
	</div>
	<div class="page-content">
		<div class="page-content-inner">
		<div class="container">
			<div class="block-card">
				{if isset($giftSet)}
					<div class="card-bank mT40" {if isset($giftSet.image.640)}style="background-image: url('{$giftSet.image.640}');height: 174px"{/if} data-card="{$card.number}" onclick="_.mod.browse.showNumberCard(this)">
						<div class="card-number">
							**** **** **** {$card.number|substr:12:4}
						</div>
					</div>
				{else}
					<div class="card-bank mT40" data-card="{$card.number}" onclick="_.mod.browse.showNumberCard(this)">
						<div class="card-number">
							**** **** **** {$card.number|substr:12:4}
						</div>
					</div>
				{/if}
			</div>
			<div class="mT30"></div>

			<div class=" f17 w320 text-center center-content f-400 lh">
				Đây là thẻ quà tặng điện tử UrBox.<br>
				Bạn có thể đổi quà tại hơn 150 thương hiệu<br> cho đến khi hết số dư trên thẻ<br>
			</div>
			<div class="w80p center-content mB40">
				<div class="mT40 pT10 mB30">
					<a href="/guide" class="btn btn-active-white w100p tutorial-open-btn">Xem hướng dẫn</a>
				</div>
				<a class="f14 f-400 display-block w100p external" href="tel:{UB_CUSTOMER_PHONE_VALUE}">
					Hotline: <span class="color-blue fr">{UB_CUSTOMER_PHONE}</span>
				</a>
				<hr class="hr">
				<a class="f14 f-400 display-block w100p external" href="mailto:<EMAIL>">
					Email: <span class="color-blue fr"><EMAIL></span>
				</a>
				<hr class="hr">
			</div>
		</div>
		</div>
	</div>

</div>
