<?php
class ULogInForm extends Form
{

	function draw() {
		global $display; $class = System::$data['namespace'];
        $token = System::$data['url']['query'][1];
		$token = preg_replace('/[^A-Za-z0-9\-]/', '', $token);
        $display->add('token',$token);
        $display->add('card',System::$data['card']);
        $display->add('app',System::$data['apps']);
		return $display->output(System::$data['version']."ULogIn");
	}
}