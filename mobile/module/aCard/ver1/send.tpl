<div class="page" data-name="success">
    <div class="navbar">
        <div class="navbar-inner">
            <div class="my-point w50p fl">
                Số dư <span class="color-spruce display-block">{$card.money|number_format} {VIEW_CURRENCY}</span>
            </div>
            <div class="my-voucher w50p fr">
                <a href="/card/{$card.token}/account" class="bg-paleGrey fr">
                    Quà của tôi {if $numOrder>0}<span>{$numOrder}</span>{/if}
                    <img src="{$smarty.const.WEB_ROOT}/mobile/style/card/ver1/images/arrow-right.svg" alt="">
                </a>
            </div>
        </div>
    </div>
   {* <div class="toolbar tabbar toolbar-bottom-md">
        <div class="toolbar-inner">
            <a style="width: 100%" target="_blank" href="{$cartLink}" class="button button-violet  pL20 pR20 external">Use now</a>
            *}{*<a href="javascript:void(0)" class="button button-violet w45p fr">Lưu về màn hình</a>*}{*
        </div>
    </div>*}
    <div class="page-content">
        <div class="page-content-inner">
        <div class="container">
            <div class="redeem-success">
                <img src="{$cart.gift.image}" class="detail-img" alt="">
                <div class="detail-content">
                    <div class="gift-name">{$cart.gift.title}</div>
                    <div class="gift-price color-spruce">Mua quà thành công</div>
                    <div class="gift-note">{$cart.gift.brand.title}</div>
                </div>
            </div>
        </div>
        <hr class="hr mT25 mB15">
        <div class="container">
            <input type="hidden" value="{$cart.id}" name="id_loyal_order" id="id_loyal_order"/>
            <input type="hidden" value="{$card.token}" name="token" id="token"/>
            <div class = "buttons-row">
                <a href = "#tab1" class = "tab-link tab-link-active">Lưu về điện thoại</a>
                <a href = "#tab2" class = "tab-link">Tặng quà</a>
            </div>
            <div class="tabs voucher-tab mB20">
                <div style="height: auto" class="tab tab-active" id="tab1">
                    <div class="tab-form">
                        <div id="jsPushTab" class="input">
                            <input type="text" name="yourPhone" id="yourPhone" placeholder="Số điện thoại của bạn">
                            <div class="div-error">Sai</div>
                        </div>
                        {*button-green*}
                        <a href="javascript:_.mod.send.saveToPhone('{$cart.gift.image}','{$cart.gift.title}','20/20/2020')" class="btn-save button-violet">
                            <img src="{$smarty.const.WEB_ROOT}mobile/style/card/ver1/images/icon-savetophone.png" alt="">Lưu về điện thoại</a>
                    </div>
                </div>
                <div style="height: auto" class="tab" id="tab2">
                    <div class="tab-form">
                        <div id="jsPushTabAs" class="input input2">
                            <input type="text" name="friendName" id="friendName" placeholder="Tên của bạn">
                            <input type="text" name="friendPhone" id="friendPhone" placeholder="Số điện thoại người nhận">
                            <textarea name="friendMessage" id="friendMessage"  placeholder="Lời nhắn (không bắt buộc)"></textarea>
                            <div class="div-error">Sai</div>
                        </div>

                        <a href="javascript:_.mod.send.sendAsGift('{$cart.gift.image}','{$cart.gift.title}','20/20/2020')" class="btn-save button-violet">
                            <img src="{$smarty.const.WEB_ROOT}mobile/style/card/ver1/images/icon-send-as-gift.png" alt="">
                            Tặng quà
                        </a>
                    </div>
                </div>
            </div>
            <a style="width: 100%;margin-bottom: 50px" target="_blank" href="{$cartLink}" class="button button-violet  pL20 pR20 external" >Sử dụng ngay</a>
        </div>
        </div>
    </div>

</div>
<script>
    let token = '{$token}';
</script>