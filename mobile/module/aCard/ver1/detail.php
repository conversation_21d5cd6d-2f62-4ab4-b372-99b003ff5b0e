<?php
class DetailForm extends Form
{

    function draw() {
        global $display; $class = System::$data['namespace'];

        $token = !empty(System::$data['url']['query'][1])? System::$data['url']['query'][1] : "";
		$token = preg_replace('/[^A-Za-z0-9\-]/', '', $token);
		$id = Url::getParamInt('id',0);
		$cart_detail_id = Url::getParamInt('expired',0);
        $card_id = System::$data['card']['id'];
        $data = BalanceVer1::detailVoucher($card_id,$id,$cart_detail_id);
        $return = 1;
        if(count($data['gifts']) < 1){
            $return = 2;
        }
        $display->add('data',$data);
        $display->add('card',System::$data['card']);
        $display->add('token',$token);
        $display->add('return',$return);
        $display->add('brand_id',$id);
        if(count($data['gifts']) !=1)
            return $display->output(System::$data['version']."detail");
        return $display->output(System::$data['version']."detail-one");
    }
}