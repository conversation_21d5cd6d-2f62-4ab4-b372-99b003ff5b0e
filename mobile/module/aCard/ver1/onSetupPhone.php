<?php
class OnSetupPhoneForm extends Form
{

	function draw() {
		global $display; $class = System::$data['namespace'];
        $token = System::$data['url']['query'][1];
		$token = preg_replace('/[^A-Za-z0-9\-]/', '', $token);
        $display->add('token',$token);
        $useIdentity = 1;
        $display->add('useIdentity',$useIdentity);
        $display->add('app',System::$data['apps']);
        $display->add('card',System::$data['card']);
		return $display->output(System::$data['version']."onSetupPhone");
	}
}