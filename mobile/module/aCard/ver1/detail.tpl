<div class="page" data-name="detail">
    <div class="navbar">
        <div class="navbar-inner">
            <div class="top-head w100p">
                <div class="w50p fl">
                    <a href="javascript:void(0)" id="backlink" class="back left"><img src="{$smarty.const.WEB_ROOT}mobile/style/card/ver1/images/icon-back.svg" alt=""> Quay lại</a>
                </div>
                <div class="my-point w50p fr text-align-right">
                    <span class="f-300 f12">Số dư</span> <span class="color-spruce f14 f-bold">{$card.money|number_format}{VIEW_CURRENCY}</span>
                </div>
            </div>
            <div class="head-tab w100p absolute bottom-0 text-align-center">
                <a href="javascript:void(0)" onclick="_.click.link('/card/{$card.token}')" class="w50p fl f14">Đ<PERSON>i voucher</a>
                <a href="javascript:void(0)" onclick="_.click.link('/card/{$card.token}?page=account')" class="w50p fr f14 active">Voucher của tôi</a>
            </div>
        </div>
    </div>
    <div class="page-content">
        <div class="page-content-inner">
        <div class="container">
            <div class="swiper-container mT20 pL5 pT5 pB5" style="padding-right: 5px;">
                <!-- Slides wrapper -->
                <div class="swiper-wrapper">
                    <!-- Slides -->
                    <input type="hidden" id="return_page" value="{$return}">
                    {assign var="expired_day" value=(1*86400) }
                    {foreach from=$data.gifts item=$item}
                    <div class="swiper-slide gift-swiper">
                            <div class="gift-detail">
                                <div class="detail-img" style="background-image:url('{$item.image}')"></div>
                                <div class="detail-content pL10 pR10">
                                    <div class="gift-name f13 f-300"><a target="_blank" class="external" href="{$item.link}">{$item.title}</a></div>
                                    <div class="gift-price color-spruce f13 f-bold mB20">
                                        {if !in_array($item.type,[10,11])}
                                            {$item.price|number_format:0:",":"."} {"đ"|t}
                                        {/if}
                                    </div>
                                </div>
                                <div class="detail-new {if $smarty.now - $item.created <= $expired_day} new{/if}">
                                </div>

                                <div class="gift-code mB30">
                                    {if $item.code_type == 2}
                                    <div class="manual_used cur_pointer display-flex align-items-center justify-content-center mT20" onclick="_.click.setUsed({$item.cart_detail_id})">
                                        {if $item.delivery != 3}
                                            <img src="{$smarty.const.WEB_ROOT}mobile/style/card/ver1/images/img-set-used.svg" class="" alt="">
                                        {else}
                                            <img src="{$smarty.const.WEB_ROOT}mobile/style/card/ver1/images/img-used.svg" class="" alt="">
                                        {/if}
                                        <div class="manual_content pL5">
                                            <span class="f-400 f10 display-block">Bạn đã sử dụng quà?</span>
                                            <span class="f-300 f9 display-block">Hãy bấm vào đây sau khi sử dụng</span>
                                        </div>
                                    </div>
                                    {/if}

                                    {if $item.code !=""}
                                        <input type="hidden" value="{$item.showbarcode}" id="codeType">
                                        <img class="mT20" src="{$smarty.const.WEB_ROOT}mobile/style/card/ver1/images/logo-ubox-small.svg" alt="">
                                        {if $item.showbarcode==0}
                                            <div class="qr_codeBoxBar_copy qr_copy cur_pointer" data-id="qrCopy{$item.cart_detail_id}">
                                                <div class="qr_codeBoxBar_copy_text f-bold">SAO CHÉP</div>
                                                <div class="code-format">{$item.code}</div>
                                                <input value="{$item.code}" readonly type="text" class="qr_codeBoxBar_text hidden-area" tabindex='-1' aria-hidden='true' id="qrCopy{$item.cart_detail_id}" />
                                            </div>
                                        {else}
                                            <div class="voucher-code-item-code-qr">
                                                {if $item.showbarcode == 2}
                                                    {*2: bar code*}
                                                    <div class="f22 f-700 text-align-center mT10">{$item.code}</div>
                                                    <svg
                                                            class="barcode code-list"
                                                            jsbarcode-value="{$item.code}"
                                                            jsbarcode-textmargin="0"
                                                            jsbarcode-fontoptions="bold"
                                                            jsbarcode-displayValue="false"
                                                            data-value="" id="qrcode{$item.cart_detail_id}"></svg>
                                                {elseif $item.showbarcode == 5}
                                                    <div class="code-img code-list code-qr" data-value="{$item.qrCode}" id="qrcode{$item.cart_detail_id}"></div>
                                                    <div class="color-gray f12">
                                                        <div class="display-flex justify-content-center">
                                                            <img src="/mobile/style/card/images/serial.svg" class="w-16 mR5" alt="">
                                                            <span>Số serial: <strong class="color-serial f-600">{$item.serial}</strong></span>
                                                        </div>
                                                        <div>
                                                            <span>Mã PIN: <strong class="color-serial f-600">{$item.pin}</strong></span>
                                                        </div>
                                                    </div>
                                                {else}
                                                    <div class="f22 f-700 text-align-center mT10 mB20">{$item.code}</div>
                                                    <div class="code-img code-list code-qr" data-value="{$item.code}" id="qrcode{$item.cart_detail_id}"></div>
                                                {/if}
                                            </div>
                                        {/if}


                                    {else}
                                        <div class="code-name text-align-center mT10 pT20">
                                            <a href="{$item.link}" class="external color-blue" target="_blank">Lấy code</a>

                                        </div>
                                        <img class="code-img" src="{$smarty.const.WEB_ROOT}mobile/style/card/ver1/images/blank.png" alt="">
                                    {/if}
                                    <div class="color-gray mT10 f12">
                                        <img src="/mobile/style/card/images/ico-notice.png" width="20" class="w10" alt="">
                                        Không cung cấp ảnh chụp màn hình cho<br>nhân viên khi thanh toán</div>
                                    <div class="color-gray mT20 f13 italic text-align-center">Hạn sử dụng: <span class="color-red">
                                            {if $item.expired != 0}
                                            {$item.expired|date_format:"d/m/Y"}
                                            {else}
                                                {if isset($item.expired)}
                                                    Vô thời hạn
                                                {else}
                                                    &nbsp;
                                                {/if}

                                            {/if}
                                        </span></div>
                                    {if $item.valid_time != 0}
                                        <div class="mT5 f12">
                                            <span class="color-gray">{'Hiệu lực sử dụng từ ngày'|t}:&nbsp;</span>
                                            <span class="color-blue f-500">
                                                {$item.valid_time|date_format:"d/m/Y"}
                                            </span>
                                        </div>
                                    {/if}
                                    <div class="code-gray f13 text-align-center mT5">Hotline: <a class="color-blue" href="tel:{UB_CUSTOMER_PHONE_VALUE}">{UB_CUSTOMER_PHONE}</a></div>
                                    <div class="f11 f-300 w75p text-align-center center-content mT10">
                                        Khách hàng vui lòng đưa link có bao gồm code cho nhân viên tại cửa hàng để sử dụng quà.
                                    </div>

                                    {if $item.type != GIFT_TYPE_RECEIVER}
                                        {if $item.code_status==1}
                                            {if $item.showbarcode!=0}
                                            <div class="code-notes mT30 mB30">
                                                <span class="mB10"></span>
                                                <a href="{$item.link_active}" target="_blank" class="btn btn-active-code external w90p mR10 mL10 f13" style="line-height: inherit;">Kích hoạt code
                                                <span class="display-block f10">dành cho nhân viên cửa hàng</span>
                                                </a>
                                            </div>
                                            {/if}
                                            <div class=" cur_pointer qr_copy" data-id="qrCopyLink{$item.cart_detail_id}">
                                                <div class="qr_codeBoxBar_copy_text f10 f-400 over-hidden center-content">
                                                    <img style="padding-top: 3px;" src="{$smarty.const.WEB_ROOT}mobile/style/card/ver1/images/copy.svg" width="24"class="fl" alt="">
                                                   <span> Sao chép link để lưu và tặng quà</span>
                                                    <span class="color-blue f10 f-300 link-hidden" href="{$item.link}">{$item.link}</span>
                                                </div>
                                                <input value="{$item.link}" readonly type="text" class="qr_codeBoxBar_text hidden-area" tabindex='-1' aria-hidden='true' id="qrCopyLink{$item.cart_detail_id}" />
                                            </div>
                                        {/if}
                                    {else}
                                        <div class="cur_pointer p10 mx qr_codeBoxBar_copy">
                                            <div class="">
                                                <a href="//{DOMAIN_NAME}/receiver/{$item.receive_code}" class="external justify-content-center display-flex align-items-center f12 f-500 over-hidden" target="_blank">
                                                    <img style="padding-top: 3px;" src="{$smarty.const.WEB_ROOT}mobile/style/card/ver1/images/copy.svg" width="24" class="fl mR5 w20" alt="">
                                                    <span> {'Sử dụng '|t}</span>
                                                </a>
                                            </div>
                                        </div>
                                    {/if}

                                </div>
                            </div>
                    </div>
                    {/foreach}
                </div>
                <!-- Pagination, if required -->
            </div>
            <div class="mT20">
                <h4 class="title">Điều kiện sử dụng</h4>
                <div class="pT0 text-align-justify f13">
                    <div class="gift-term" id='toggle-term'>
                        {$data.note}
                       <div id='toggle-click'><span> Xem thêm</span></div>
                    </div>
                </div>
            </div>
            <div class="mT20 mB50 pointer" onclick='_.click.link("/card/{$token}?page=brandOffice&id={$brand_id}&gift_id={$data.gift_id}")'>
                <span class="title">Vị trí cửa hàng</span>
                <div class="pT0">
                    <ul>
                        <li class="f13 f-300 ">- Sử dụng tại {$data.office|count} cửa hàng
                            <img src="{$smarty.const.WEB_ROOT}mobile/style/card/ver1/images/arrow-right.svg" class="fr" alt="">
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <a href="javascript:void(0)" onclick="_.click.link('/card/{$token}?page=welcome')" class="help-icon">Hỗ trợ</a>
        </div>
    </div>

</div>
<script>
    let token = '{$token}';
</script>