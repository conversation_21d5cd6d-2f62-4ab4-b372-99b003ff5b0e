<div class="page" data-name="account">
	<div class="navbar">
		<div class="navbar-inner">
			<div class="top-head w100p">
				<div class=" w50p fl">
					<a href="javascript:void(0)" id="backlink" class="back left"><img src="{$smarty.const.WEB_ROOT}mobile/style/card/ver1/images/icon-back.svg" alt=""> Quay lại</a>
				</div>
				<div class="my-point w50p fr text-align-right">
					<span class="f-300 f12">Số dư</span> <span class="color-spruce f14 f-bold">{$card.money|number_format}{VIEW_CURRENCY}</span>
				</div>
			</div>
			<div class="head-tab w100p absolute bottom-0 text-align-center">
				<a href="javascript:void(0)" onclick="_.click.link('/card/{$card.token}')" class="w50p fl f14">Đ<PERSON>i voucher</a>
				<a href="javascript:void(0)" class="w50p fr f14 active">Voucher của tôi</a>
			</div>
		</div>
	</div>
	<div class="page-content">
		<div class="page-content-inner">
		<div class="container">
			<div class="title mT20">Sẵn sàng sử dụng</div>
			<div class="jsboxList mT5 mB10 list media-list gift-list">
				{if isset($data.brand)}
					<ul>
						{*sắp hết hạn*}
						{foreach from=$gift_expiring.brand item=$item}
							{if $item.type == GIFT_TYPE_RECEIVER && $item.soluong  == 1}
								{assign var="onClick" value="window.open('//{DOMAIN_NAME}/receiver/{$item.receive_code}')"}
							{else}
								{assign var="onClick" value="_.click.link('/card/{$card.token}?page=detail&id={$item.id}{if $item.soluong  <=1}&expired={$item.cart_detail_id}{/if}')"}
							{/if}
							<li class="list-voucher pointer" {if $today <= $item.expired} onclick="{$onClick}" {/if}>
								<div  class="item-link bought-voucher one item-content">
									<div class="item-media"><img src="{$item.image}"/></div>
									<div class="item-inner">
										<div class="item-title f13 f-500">{$item.title}<br>
											{assign var="date_expir" value=($item.expired - $today) / 86400 }
											{if $date_expir <= 0}
												<span class="color-gray">Hết hạn sử dụng</span>
											{else}
												{if $date_expir >=1}
													<span class="f-bold f13 {if $date_expir <=5} color-red{/if}">Hạn sử dụng còn {$date_expir|round:0} ngày</span>
												{else}
													<span class="f-bold f13 {if $date_expir <=5} color-red{/if}">Hết hạn sử dụng trong hôm nay</span>
												{/if}
											{/if}
										</div>
									</div>
								</div>

							</li>
						{/foreach}
						{*dùng dc*}
						{foreach from=$data.brand item=item name=i}
							{if $item.type == GIFT_TYPE_RECEIVER && $item.soluong  == 1}
								{assign var="onClick" value="window.open('//{DOMAIN_NAME}/receiver/{$item.receive_code}')"}
							{else}
								{assign var="onClick" value="_.click.link('/card/{$card.token}?page=detail&id={$item.id}{if $item.soluong  <=1}&expired={$item.cart_detail_id}{/if}')"}
							{/if}
							<li class="list-voucher pointer {if $smarty.now - $item.created <= 86400}new{/if}" onclick="{$onClick}">
								<div  class="item-link bought-voucher {if $item.soluong  <=1}one{/if} item-content">
									<div class="item-media"><img src="{$item.image}"/></div>
									<div class="item-inner">
										<div class="item-title f13 f-500">{$item.title}
											{if $item.soluong == 1}
												<br>
												{if $item.expired === 0}
													<span class="f-bold">Vô thời hạn</span>
												{else}
													{if $item.expired != ""}
														{assign var="date_expir" value=($item.expired - $smarty.now) / 86400 }
														{if $date_expir <= 0}
															<span class="color-gray">Hết hạn sử dụng</span>
														{else}
															{if $date_expir <=5}
																<span class="f-bold f13  color-red">Hạn sử dụng còn {$date_expir|round:0} ngày</span>
															{/if}
														{/if}
													{/if}
												{/if}
											{/if}
										</div>
									</div>
									{if $item.soluong   > 1}
										<div class="item-count">{$item.soluong}</div>
									{/if}
								</div>

							</li>
						{/foreach}
						{*đã hết hạn*}
						{foreach from=$gift_expired.brand item=$item}
							<li class="list-voucher pointer" {if $today <= $item.expired} onclick="_.click.link('/card/{$token}?page=detail&id={$item.id}&expired={$item.cart_detail_id}')" {/if}>
								<div  class="item-link bought-voucher one item-content">
									<div class="item-media"><img src="{$item.image}"/></div>
									<div class="item-inner">
										<div class="item-title f13 f-500">{$item.title}<br>
											{assign var="date_expir" value=($item.expired - $today) / 86400 }
											{if $date_expir <= 0}
												<span class="color-gray">Hết hạn sử dụng</span>
											{else}
												<span class="f-bold f13 {if $date_expir <=5} color-red{/if}">Hạn sử dụng còn {$date_expir|round:0} ngày</span>
											{/if}
										</div>
									</div>
								</div>

							</li>
						{/foreach}
					</ul>
				{/if}
			</div>
			<div class="title mT20">Đã sử dụng và gửi tặng</div>
			<div class="jsboxList mT5 mB10 list media-list gift-list">
				<ul>
					{foreach from=$gift_used item=$item name=i}
						<li class="list-voucher">
							<div class="item-link bought-voucher one item-content">
								<div class="item-media"><img src="{$item.image}" alt=""></div>
								<div class="item-inner">
									<div class="item-title f13 f-500">
										<a target="_blank" href="{$item.link}" class="gift-name external color-gray f-300 f12">{$item.title}</a>
										<div class="gift-exp color-gray f-300 f12">Đã sử dụng</div>
									</div>
								</div>
							</div>
						</li>
					{/foreach}
				</ul>
			</div>
		</div>
		<a href="javascript:void(0)" onclick="_.click.link('/card/{$token}?page=welcome')" class="help-icon">Hỗ trợ</a>
		</div>
	</div>
</div>
<script>
	let token = '{$token}';
</script>