<div class="page" data-name="brandOffice">
	<div class="navbar">
		<div class="navbar-inner" style="flex-direction: row">
			<a href="javascript:void(0)" id="backlink" class="back left"><img src="{$smarty.const.WEB_ROOT}mobile/style/card/ver1/images/icon-back.svg" alt=""> Quay lại</a>
			<div class="top-head w100p pT20 text-align-center">
				<a href="javascript:void(0)" onclick="_.click.link('/card/{$card.token}')">
					{if !empty($app.logo)}
						<img src="{$app.logo}" alt="">
					{else}
						<img src="{$smarty.const.WEB_ROOT}mobile/style/card/ver1/images/logo-urbox.svg" height="30px" alt="">
					{/if}
				</a>
			</div>
		</div>
	</div>
	<div class="page-content">
		<div class="page-content-inner">
		<div class="container">
			<div class="brand_top mT10 over-hidden mB5">
				<div class="brand_top_img fl">
					<img src="{$brand.image}" alt="">
				</div>
				<div class="brand_top_content over-hidden pR10 pL10 fl">
					<div class="brand_top_title mT15">
						<span class="f-bold f16">{$brand.title}</span>
						<div class="brand_top_like hidden">
							<span class="f12 f-300">5 lượt yêu thích</span>
						</div>
					</div>
				</div>
				<div class="brand_top_like_btn mT20 fr hidden">
					<a class="btn-brand-like f14 f-300 color-spruce" href="javascript: void(0)">Yêu thích</a>
				</div>
			</div>
		</div>
		<div class="container">
			<div class="hidden swiper-container swiper-init" data-slides-per-view="auto">
				<div class="swiper-wrapper title-catalog">
					<a href="/brand" class="swiper-slide">Voucher mệnh giá</a>
					<a href="/brand-product" class="swiper-slide swiper-slide-active">Sản phẩm</a>
					<a href="/brand-discount" class="swiper-slide">Voucher giảm giá</a>
				</div>
			</div>
			<input type="hidden" value="1" id="js_total_List" name="js_total_List"/>
			<input type="hidden" value="{$per_page}" id="js_per_page" name="js_per_page"/>
			<input type="hidden" value="{$brand_id}" id="js_brand_id" name="js_brand_id"/>
			<input type="hidden" value="{$card.token}" id="js_token" name="js_token"/>
		</div>
		<div class="title display-flex flex-sb align-items-center">
			<div class="w100p">
				<div class="catalog_location border-box w100p pT10 pB10 pL20 pR20" style="background-position: center right 20px;-webkit-box-sizing: border-box;-moz-box-sizing: border-box;box-sizing: border-box;border-left: 0;border-bottom: 1px solid #f0f0f0;border-top: 1px solid #f0f0f0">
					<div id="find-location" onclick="_.click.findLocation(1)">
						<h5 class="f12 f-300 pT0 pB0 mT0 mB0">Địa điểm</h5>
						<div id="my-location" class="f14 f-mbold over-hidden my-location">{$city.title}</div>
					</div>
				</div>
			</div>
		</div>
		<div class="container">
			<div class="tabs">
				{*tab 2*}
				<div id="brand-address">
					<div class="list list-address searchbar-found">
						<ul>
							{foreach from =$brand.office item=$address}
							<li class="item-content list-office"  {if $address.show != 1}style="display: none;"{/if}>
								<div class="brand-img item-media">
									<img src="{$brand.image}" alt="">
								</div>
								<div class="item-inner no-border">
									<div class="items-content w100p">
										<div class="item-title f-500 display-block">{$brand.title} <span class="color-4a f-300 f13 fr">
												{if $address.distance !== "-"}
													{$address.distance} km
												{/if}
											</span></div>
										<div class="item-subtitle color-4a f14 f-300">{$address.address}</div>
									</div>
								</div>
							</li>
							{/foreach}
						</ul>
					</div>
				</div>
				{*hết tab 2*}
			</div>
		</div>
		</div>
	</div>
</div>
<script>
	let token = '{$token}';
</script>