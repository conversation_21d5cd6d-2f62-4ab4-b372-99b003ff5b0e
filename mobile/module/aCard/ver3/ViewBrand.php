<?php
class ViewBrandForm extends Form
{

    function draw() {
        global $display; $class = System::$data['namespace'];

        #Danh muc
        $brand_id = Url::getParamInt('id',"");
        if($brand_id <= 0) {
        	Url::redirect("card");
        }
        $card = System::$data['card'];
		BalanceVer3::setBrandToCache($brand_id);
        $brand = BalanceVer3::getBrandById($brand_id);
        $hidden_brand = 1;
		if(empty($brand)) {
			BalanceVer3::removeBrandCache($brand_id);
			$hidden_brand = 2;
			Url::redirect("card" );
		}
		$phone = System::decrypt($card['phone'],CRYPT_KEY);
		Customer::add(['phone'=>$phone]);
		$UserIdentity = [];
		$encodeUserIdentity = Card::getUserIdentity($card['id']);
		if(!empty($encodeUserIdentity)){
			$decode = System::decrypt($encodeUserIdentity);
			if($decode) {
				$UserIdentity = json_decode( $decode, true );
			}
		}
		if(empty($UserIdentity)) {
			if ($phone != "") {
				$UserIdentity = DB::fetch( "SELECT phone,identity FROM " . T_CUSTOMER . " WHERE phone='" . $phone . "'" );
			} else {
				$UserIdentity = array(
					'phone' => $phone,
					'identity' => ''
				);
			}
		}
		$display->add("UserIdentity",$UserIdentity);
        $office = BalanceVer3::getOffice($brand_id,0,0,true);
        // lấy danh sách gift detail hiển thị
        $gifts = BalanceVer3::getGiftByBrand($brand_id);
        $allowAccess = CookieLib::get("C_ALLOW_LOCATION");
        $city = BalanceVer3::getCityName();
		$display->add('hidden_brand',$hidden_brand);
		$display->add('app',System::$data['apps']);
		$display->add('gifts',$gifts);
        $display->add('allowAccess',$allowAccess);
        $display->add('brand',$brand);
        $display->add('office',$office);
        $display->add('city',$city);
        $display->add('card',$card);

        return $display->output(System::$data['version']."ViewBrand");
    }
}