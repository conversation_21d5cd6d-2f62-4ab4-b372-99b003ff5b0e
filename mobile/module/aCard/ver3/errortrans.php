<?php
class ErrorTransForm extends Form
{

    function draw() {
        global $display; $class = System::$data['namespace'];

        $token = !empty(System::$data['url']['query'][1])? System::$data['url']['query'][1] : "";
		$token = preg_replace('/[^A-Za-z0-9\-]/', '', $token);
		$id = Url::getParamInt('id',0);
        $tokens = BalanceVer3::getToken($token);
        $gift = BalanceVer3::getGift($id);
		$display->add('app',System::$data['apps']);
        $display->add('id',$id);
        $display->add('gift',$gift);
        $display->add('point',$tokens);
        $display->add('token',$token);
        return $display->output(System::$data['version']."errortrans");
    }
}