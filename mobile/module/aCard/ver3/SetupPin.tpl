<div class="page {$app.name}" data-name="setupPin">
    <div class="navbar">
        <div class="navbar-inner">
            <div class="top-head w100p text-align-center pT5">
                <div class="ur-logo">
                    <a href="javascript:void(0)">
                        {if !empty($app.logo)}
                            <img src="{$app.logo}" alt="" class="h60 icons">
                        {else}
                            <img src="{$smarty.const.WEB_ROOT}mobile/style/card/ver3/images/logo-urbox.svg" class="h60 icons" alt="">
                        {/if}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="page-content page-setup-pin">
        <div class="page-content-inner">
        <div class="mT20 container">
            {if !empty($app.image)}
                <img src="{$app.image}" class="w100p" alt="">
            {else}
                <img src="{$smarty.const.WEB_ROOT}mobile/style/card/ver3/images/bg-card.png" class="w100p" alt="">
            {/if}
        </div>
        <div class="create-pin mT30">
            <div class="container display-flex justify-content-center f-wrap">
                <div class="create-pin-content w100p">
                    <div class="create-pin-title">
                        <h3 class="text-title m-0">{"Tạo mã PIN mới"|t}</h3>
                    </div>
                    <div class="input-pin w100p display-flex justify-content-center f-wrap">
                        <div class="form-group w100p pT20">
                            <span class="text-label-input">{"Nhập mã PIN gồm 6 chữ số"|t}</span>
                            <input type="password" pattern="[0-9]*" inputmode="numeric" id="pin" class="input-otp-control" />
                        </div>
                        <div class="form-group w100p pT20 mB20">
                            <span class="text-label-input">{"Nhập lại mã PIN"|t}</span>
                            <input type="password" pattern="[0-9]*" inputmode="numeric" id="re-pin" class="input-otp-control" />
                        </div>
                        <div class="submit-otp-content w100p text-align-left">
                            <p id="message-pin" class="text-content-modal text-align-left cl-red mx"></p>
                        </div>
                        <div class="form-group mT30 w100p">
                            <a href="javascript:void(0)" class="btn btn-violet w100p" onclick="_.mod.register.validatePin()">{"Xác nhận"|t}</a>
                            <div class="contact mT10 text-align-center">
                                <span class="text-hot-line italic">{"Quý khách cần hỗ trợ? Liên hệ hotline"|t}</span>
                                <br>
                                <a target="_parent" href="tel:{UB_CUSTOMER_PHONE_VALUE}" class="hotline f20 color-blue external italic">{UB_CUSTOMER_PHONE}</a>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
        </div>
    </div>
</div>





