<?php

class SurveyForm extends Form
{
    public function __construct()
    {
    }

    public function draw()
    {
        global $display; $class = System::$data['namespace'];
        $card = System::$data['card'];
        $survey_id = Balance::checkSurvey($card['token']);
        if ($survey_id == false || $survey_id <= 0){
            Url::redirect('card');
        }

        $survey = Balance::getSurvey($survey_id);
        $display->add('survey',$survey);
        $display->add('card',$card);
		$display->add('app',System::$data['apps']);
        return $display->output(System::$data['version'].'Survey');
    }
}