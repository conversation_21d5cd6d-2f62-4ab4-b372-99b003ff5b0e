<div class="page {$app.name}" data-name="viewbrand">
	<div class="navbar">
		<div class="navbar-inner">
			<a href="javascript:void(0)" id="backlink" class="back left f14"><img src="{$smarty.const.WEB_ROOT}mobile/style/card/ver3/images/icon-back.svg" alt="">{"Quay lại"|t}</a>
			<span class="display-flex align-items-center header-point">
                <span class="f15 f-500 pR10 card-money">{$card.money|number_format:0:",":"."} {VIEW_CURRENCY}</span>
                <a href="javascript:void(0)" onclick="_.click.link('/card?page=account')" class="btn-my-gift f11 f-500">{"Quà của tôi"|t}</a>
             </span>
		</div>
	</div>
	<div class="page-content page-view-brand">
		<div class="page-content-inner">
{*		<img src="{$brand.banner}" alt="" class="w100p">*}
		<input type="hidden" value="{$card.needActive}" id="js_card_type" name="js_card_type"/>
		<input type="hidden" value="{$UserIdentity.phone}" id="js_phone_val" name="js_phone_val"/>
		<input type="hidden" value="{$UserIdentity.identity}" id="js_identity_val" name="js_identity_val"/>
		<input type="hidden" value="{$card.token}" id="js_token_val" name="js_token_val"/>
		<div class="container">
			<div class="display-flex mT20">
				<div class="box-shadow p5 w60 box-sizing" style="font-size:0">
				<img src="{$brand.logo}" class="" alt="">
				</div>
				<div class="mL10">
					<span class="f-500 display-block mT10 f18">{$brand.title|t}</span>
					<span class="f12 color-gray-2">{$brand.category|t}</span>
					<div class="brand_top_like hidden">
						<span class="f12 f-300">5 {"lượt yêu thích"|t}</span>
					</div>
					<div class="brand_top_like_btn mT20 fr hidden">
						<a class="btn-brand-like f14 color-app" href="javascript: void(0)">{"Yêu thích"|t}</a>
					</div>
				</div>
			</div>


		</div>
		<div class="border-bottom mT20"></div>
		{if $hidden_brand == 1}
		{if $gifts}
			<div class="list media-list over-hidden m0 pB10">
				<ul id="jsBlockGift">
					{foreach from=$gifts item=item name=i}
						<li class="container border-bottom pB20">
							<div class="item-content pT20">
								{if $item.isPhysical == 2}
									<div class="item-media voucher-img brand-voucher" style="background-image: url('{$item.image}')" onclick="_.click.link('/card?page=gift&id={$item.id}')"></div>
								{else}
									<div class="item-media voucher-img brand-voucher" style="background-image: url('{$item.image}')" onclick="_.click.getGift({$item.id})"></div>
								{/if}
								<div class="item-inner">
									{if $item.isPhysical == 2}
										<div class="f14 h34 lh over-hidden" onclick="_.click.link('/card?page=gift&id={$item.id}')">{$item.title}</div>
										<div class="f-600 color-app f14" onclick="_.click.link('/card?page=gift&id={$item.id}')">{$item.price|number_format:0:',':'.'} {VIEW_CURRENCY}</div>
									{else}
										<div class="f14 h34 lh over-hidden" onclick="_.click.getGift({$item.id})">{$item.title}</div>
										<div class="f-600 color-app f14" onclick="_.click.getGift({$item.id})">{$item.price|number_format:0:',':'.'} {VIEW_CURRENCY}</div>
									{/if}

									{if $card['expired_time'] < $smarty.const.TIME_NOW}
										{if $item.isPhysical == 2}
											<a onclick="_.click.link('/card?page=gift&id={$item.id}')" class="color-app added-class f10" href="javascript:void(0)">{"Chọn quà"|t}</a>
										{else}
											<a onclick="_.click.getGift({$item.id})" class="color-app added-class f10" href="javascript:void(0)">{"Chọn quà"|t}</a>
										{/if}
									{/if}
								</div>
							</div>
						</li>
					{/foreach}
				</ul>

			</div>
		{else}
			<h5 class="mT15 mB20 container">{"Thương hiệu này hiện không có voucher nào áp dụng được ở tỉnh thành bạn đang chọn."|t}</h5>
		{/if}
		{if $brand.mota}
			<div class="container">
				<p class="f18 mB10">{"Thông tin thương hiệu"|t}</p>
				<div class="f14 " id="mota">
					{$brand.mota}
				</div>
{*				<a href="javascript:void(0)" class="seeMore shortDescription" data-id="mota">*}
{*					<div class="moreEffect"></div>*}
{*					<span class="f14">Xem thêm</span>*}
{*				</a>*}
			</div>
			<div class="border-bottom mT20"></div>
		{/if}
			{include file="../Element/fee_ops_popup.tpl"}
		{if $brand.online == 1}
			{if $brand.openTime && $brand.priceRange}
				<div class="container display-flex pT10">
					{if $brand.priceRange}
					<div class="box-shadow w100 h100 mR20 display-flex justify-content-center">
						<div class="text-align-center mT20">
							<img src="{$smarty.const.WEB_ROOT}mobile/style/card/ver3/images/img-priceRange.svg" class="icons" alt="">
							<div class="f12 pL10 h30 display-flex align-items-center pR10 box-sizing">{$brand.priceRange}</div>
						</div>
					</div>

					{/if}
					{if $brand.openTime}
					<div class="box-shadow w100 h100 display-flex justify-content-center">
						<div class="text-align-center mT20">
							<img src="{$smarty.const.WEB_ROOT}mobile/style/card/ver3/images/img-openTime.svg" class="icons" alt="">
							<div class="f12 pL10 h30 display-flex align-items-center pR10 box-sizing">{$brand.openTime}</div>
						</div>
					</div>
					{/if}
				</div>
				<div class="border-bottom mT20"></div>
			{/if}

		<div class="container">
			<div class="list list-address">
				{if $office}
					{if $allowAccess == 1}
						<div class="f18 lh20 mB20">{"Cửa hàng gần đây"|t}</div>
					{else}
						<div class="f18 lh20 mB20">{"Danh sách cửa hàng"|t}</div>
					{/if}
				{/if}

				<ul>
					{foreach from =$office item=$address}
					<li class="p15 mB15 list-office display-flex justify-content-space-between align-items-flex-start f12 pointer" onclick="_.click.link('/card?page=officeloc&id={$address.id}')">
						<span>{$address.address}</span>
						{if $address.isApply == 1}
						<span class="flex-shrink-0 pL10 color-red f10 w50 text-align-center">{"Không<br>áp dụng"|t}</span>
						{else}
							{if $address.distance != ""}
								<span class="flex-shrink-0 pL10 w50">{$address.distance} km</span>
							{/if}
						{/if}

					</li>
					{/foreach}
				</ul>
			</div>
		</div>
		{/if}

		{else}
			<p class="color-red container mT30">Thương hiệu đang tạm ẩn.</p>
		{/if}
		</div>
	</div>
</div>
