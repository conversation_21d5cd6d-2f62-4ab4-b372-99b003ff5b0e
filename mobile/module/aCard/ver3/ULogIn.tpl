<div class="page {$app.name}" data-name="ulogin">
    <div class="navbar">
        <div class="navbar-inner">
            <div class="top-head w100p text-align-center pT5">
                <div class="ur-logo">
                    <a href="javascript:void(0)">
                        {if !empty($app.logo)}
                            <img src="{$app.logo}" alt="" class="h60 icons">
                        {else}
                            <img src="{$smarty.const.WEB_ROOT}mobile/style/card/ver3/images/logo-urbox.svg" class="h60 icons" alt="">
                        {/if}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="page-content page-upin">
        <div class="page-content-inner">
        <div class="container">
            <div class="block-card">
                <div class="card-bank mT40">
                    {if !empty($app.image)}
                        <img src="{$app.image}" class="w100p" alt="">
                    {else}
                        <img src="{$smarty.const.WEB_ROOT}mobile/style/card/ver3/images/bg-card.png" class="w100p" alt="">
                    {/if}
                    <div class="card-number">
                        **** **** **** {$card.number|substr:12:4}
                    </div>
                </div>
            </div>
        </div>
        <div class="input-pin">
            <div class="container d-f-center f-wrap">
                <div class="input-pin-content w100p">
                    <div class="f16 text-align-center mT10">Nhập mã PIN được cấp để kích hoạt thẻ</div>
                    <div class="input-pin w100p d-f-center f-wrap mT30">
                        <div class="form-group w100p">
                            <span class="text-label-input display-block">{"Nhập mã PIN"|t}</span>

                            <input pattern="[0-9]*"  name="pin" id="input-ulogin" type="password" value="" class="input-otp-control" />

                        </div>
                        <div class="submit-otp-content w100p text-align-left mT10">
                            <p class="text-content-modal text-align-left cl-red m-0 hidden" id="error-login"></p>
                        </div>
                        <div class="form-group w100p mT20">
                            <a href="javascript: void(0)" onclick="_.mod.register.ulogin()" class="btn btn-active w100p" >{"Truy cập"|t}</a>
                            <div class="card-contact mT10 text-align-center">
                                <span class="text-hot-line f-s-i">{"Quý khách cần hỗ trợ? Liên hệ hotline"|t}</span>
                                <br>
                                <a target="_parent" href="tel:{UB_CUSTOMER_PHONE_VALUE}" class="text-hot-line-number italic external"><span>{UB_CUSTOMER_PHONE}</span></a>
                            </div>
                        </div>
                        <div class="log-out w100p mT20 mB50">
                            <span>{"Quý khách có thẻ khác?"|t}</span> <a href="javascript:;" onclick="_.app.dialog.open('#dialog-logout')" class=" cl-blue f16 text-align-left">{'Đăng xuất'|t}</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        </div>

        <div class="dialog" id="dialog-logout" data-backdrop-close="true" data-backdrop="static">
            <div class="dialog-inner ">
                <p class="f15 text-align-center mT30 pL30 pR30 f-300">{"Quý khách có chắc chắn muốn đăng xuất?"|t}</p>
                <div class="mT30 mB10 display-flex align-items-center text-align-center" style="flex-direction: column">
                    <a href="javascript:void(0)" onclick="_.click.logout(2)" class="btn btn-active w200">{"Đăng xuất"|t}</a>
                    <a href="javascript:void(0)" onclick="_.app.dialog.close()" class="mT30 w200">{"Quay lại"|t}</a>

                </div>
            </div>
        </div>
	</div>
</div>
