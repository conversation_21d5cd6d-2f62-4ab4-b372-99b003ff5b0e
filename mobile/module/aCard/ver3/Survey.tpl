<div class="page {$app.name}" data-name="survey">
    <div class="navbar">
        <div class="navbar-inner">
            <div class="top-head display-flex justify-content-center mT5 w100p">
                {if !empty($app.logo)}
                    <img src="{$app.logo}" alt="" height="50" style="width:auto">
                {else}
                    <img src="/mobile/style/card/ver3/images/logo-urbox.svg" alt="" height="60" style="width:auto">
                {/if}
            </div>

        </div>
    </div>
    <div class="page-content page-survey">
        <div class="page-content-inner">
        <div class="container">
            <div class="f22 mT30">
                {$survey.survey.title}</div>
            <div class="mT30">
                {assign var='money' value=$card.money|number_format:0:",":"."}
                {$survey.survey.description|sprintf:$money|unescape: 'html'}
            </div>
            <div class="mT20 w100p center-content">
                {if !empty($survey.survey.image)}
                    <img class="w100p" src="{$survey.survey.image}" alt="">
                {/if}
            </div>
            <form action="" id="survey-form" class="form-ajax-submit" type="POST">
                <input type="hidden" name="token" value="{$card.token}">
                <input type="hidden" name="survey_id" value="{$survey.survey.id}">
            {foreach from=$survey.question item=$question}
                <div class="mT20">
                    {if $question.type == 1}
                        {include file=$smarty.const.TPL_SURVEY_1 item=$question}
                    {elseif $question.type == 2}
                        {include file=$smarty.const.TPL_SURVEY_2 item=$question}
                    {elseif $question.type == 3}
                        {include file=$smarty.const.TPL_SURVEY_3 item=$question}
                    {elseif $question.type == 4}
                        {include file=$smarty.const.TPL_SURVEY_4 item=$question}
                    {/if}
                </div>
            {/foreach}

            <div class="mT20">
                <span class="f14 italic"><span class="color-red">*</span> {"Câu hỏi bắt buộc"|t}</span>
            </div>
            <div class="mT30 mB30">
                <a  id="submit-survey-form" class="btn btn-active w100p convert-form-to-data">{"Tiếp tục"|t}</a>
            </div>
            </form>
        </div>
        <a href="javascript:void(0)" onclick="_.click.link('/card?page=help')" class="help-icon">{"Hỗ trợ"|t}</a>
        </div>
    </div>
</div>