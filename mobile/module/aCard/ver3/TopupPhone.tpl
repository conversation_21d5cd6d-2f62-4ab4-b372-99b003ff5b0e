<div class="page {$app.name}">
	<div class="no-border navbar">
		<div class="navbar-inner">
			<a href="javascript:void(0)" id="backlink" class="back left f14"><img src="{$smarty.const.WEB_ROOT}mobile/style/card/ver3/images/icon-back.svg" alt="">{"Quay lại"|t}</a>
			<span class="display-flex align-items-center header-point">
                <span class="f15 f-500 pR10 card-money">{$card.money|number_format:0:",":"."} {VIEW_CURRENCY}</span>
                <a href="javascript:void(0)" onclick="_.click.link('/card?page=account')" class="btn-my-gift f11 f-500">{"Quà của tôi"|t}</a>
             </span>
		</div>
	</div>

	<div class="page-content page-topup">
		<div class="page-content-inner">
		<div class="card-type">
			<ul>
				<li class="w50p text-align-center {if $giftType == $smarty.const.GIFT_TYPE_TOPUP}active-tab{/if}"><a href="javascript:void(0)" class="f14 text-align-center" onclick="_.click.link('/card?page=topup')">Nạp tiền trực tiếp</a></li>
				<li class="w50p text-align-center {if $giftType == $smarty.const.GIFT_TYPE_PHONE}active-tab{/if}"><a href="javascript:void(0)" class="f14 text-align-center" onclick="_.click.link('/card?page=topup&type=card')">Thẻ cào điện thoại</a></li>
				{*				<li {if $giftType==5}class="active-tab"{/if}><a href="javascript:void(0)" class="f14" onclick="_.click.link('/card?page=topup&type=data')">Thẻ 3G/4G</a></li>*}
			</ul>
		</div>
		<div class="container">
			<div class="f-500 f16 mT20">
				{$titleData.labelPhone}
			</div>
			<select name="network" class="mT20" id="choose_network">
				<option value="">Chọn nhà mạng</option>
				{foreach from=$gifts item=$gift}
					<option value="{$gift.id}">{$gift.name}</option>
				{/foreach}
			</select>
			<div class="mT5 color-red f12 hidden" id="error-network">Bạn chưa chọn nhà mạng</div>
			<input type="hidden" value="" id="selected_network">
			<div class="f-500 f16 mT20 mB20">
				{$titleData.labelPrice}
			</div>
			<ul class="display-flex justify-content-space-between flex-wrap price-container">
				{foreach from=$gifts item=$gift key=$k}
					{foreach from=$gift.detail item=$price}
						<li class="network network-{$gift.id} hidden ">
					<div data-id="{$price.id}" class="list-price">{$price.price|number_format:0:",":"."}</div>
				</li>
				{/foreach}
				{/foreach}
			</ul>
			<div class="mT5 color-red f12 hidden" id="error-price">Bạn chưa chọn mệnh giá</div>
			<div class="mT20">
				<div class="display-flex justify-content-space-between">
					<span>Số lượng</span>
					<div>
						<div class="stepper stepper-init">
							<div class="stepper-button-minus"></div>
							<div class="stepper-input-wrap">
								<input name="qty" id="qty" type="text" value="1" min="1" max="10" step="1" readonly>
							</div>
							<div class="stepper-button-plus"></div>
						</div>
					</div>
				</div>
				<div class="mT5 color-red f12 hidden" id="error-qty">Số lượng tối thiểu là 1 và tối đa là 10</div>
			</div>
			<input type="hidden" value="" id="hidden-price">
			<div class="mT20 mB20">
				<a href="javascript:void(0)" onclick="_.topup.cardPhone()" class="btn btn-active w100p">Thanh toán</a>
			</div>
		</div>
		<div class="sheet-modal sheet-modalConfirm sheet-topup" id="sheet-topup">
					<div class="sheet-modal-inner">
						<div class="page-content">
							<div class="sheetHr"></div>
							<div class="card-item mT20 mB50">
								<div class="display-flex justify-content-space-between pT10 pB10">
									<div class= "f-400 f14">Mệnh giá</div>
									<div class=" f14 f-500" id="selected-price"></div>
								</div>
								<div class="border-bottom"></div>
								<div class="display-flex justify-content-space-between pT10 pB10">
									<div class= "f-400 f14">Nhà mạng</div>
									<div class="f14 f-500" id="selected-network"></div>
								</div>
								<div class="border-bottom"></div>
								<div class="display-flex justify-content-space-between pT10 pB10">
									<div class= "f-400 f14">Số lượng</div>
									<div class="f14 f-500" id="selected-qty">1</div>
								</div>
								<div class="border-bottom"></div>
							</div>

							<div class="toolbar toolbar-bottom">
								<div class="toolbar-inner p0">
									<a onclick='_.topup.doTopup({$giftType})' id="btn-receiver" class="btn btn-active w100p display-flex justify-content-space-between pL20 pR20"><span class='total-title f16 fl pL10 f-600'>Nạp tiền</span> <span class='f-400 f16  fr pR10'><span id='total-amount'>0</span><span></span></span></a>
								</div>
							</div>
						</div>
					</div>
				</div>
		</div>
 	</div>
</div>