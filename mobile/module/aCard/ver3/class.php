<?php

class App
{
    function __construct($block,$options = [])
    {
        Module::initialize($block);
            Form::addCss("mobile/style/card/{$options['version']}default.css");
            Form::addCss("mobile/style/card/{$options['version']}root.css");
            Form::addCss("mobile/style/card/{$options['version']}style.css");
            Form::addJs('mobile/libs/_f7/plugins/keyboard/js/framework7.keypad.js');
            Form::addJs("mobile/javascript/card/{$options['version']}routes.js");
            Form::addJs("mobile/javascript/card/{$options['version']}app.js");
            Form::addCss('mobile/libs/_f7/plugins/keyboard/css/framework7.keypad.css');
            Form::addCss("mobile/style/card/card.css");
        header('Location: https://page.urbox.vn');
        return;
        //init style and js
		$token = $options['token'];
        if (empty($token)) {
            require_once 'Register.php';
            $form = 'RegisterForm';
        } else {
			$page = strtolower($options["page"]);
            $card = Card::getByToken($token);
            CookieLib::set('cardNum', $token,time()+3600*24*15,true,false);
			if (!in_array($card['status'], [CARD_ACTIVE, CARD_DEACTIVATE,CARD_WALLET])) {
				Url::redirect('home');
			}
            Card::trackingCard($card['id']);
            if ($card['app_id'] == 0) {
                $card['app_id'] = 34;
            }

            $app = Balance::getApp($card);
            System::$data['apps'] = $app;

            if ($card['gift_id'] == 0) {
                $card['gift_id'] = $app['gift_id'];
            }
            System::$data['card'] = $card;
            $file_name = "";
            if ($app != false && !empty($app)) {
                if (!is_dir('./_cache/file/card/')) {
                    mkdir('./_cache/file/card', 0755);
                }
                if (!is_dir('./_cache/file/card/css/')) {
                    mkdir('./_cache/file/card/css', 0755);
                }
                $file_name = "./_cache/file/card/css/root-" . md5($app['id'] . $app['name']) . '.css';
                if (!file_exists($file_name)) {
                    if (!empty($app['style_card'])) {
                        $f = fopen($file_name, 'w');
                        $content = $app['style_card'];
                        fwrite($f, $content);
                        fclose($f);
                    }
                }
            }

            if (is_file($file_name)) {
                Form::addCss($file_name);
            }
            System::$data['card'] = $card;
            if($card['status'] == CARD_ACTIVE){
                if($card['phone'] != ''){
                    if(Card::preAdd($card,$card['phone'])){
                        System::$data['card'] = Card::getByToken($token);
                    }
                }
                if(Card::topupCardByGiftCode($card)){
                    System::$data['card'] = Card::getByToken($token);
                }
            }
            $page = strtolower($page);
            if(!Balance::checkCardPrefix($card['number'])){
                $page = 'open_in_app';
            }
            switch ($page) {
                case 'survey':
                    require_once 'Survey.php';
                    $form = 'SurveyForm';
                    break;
                case 'catalog':
                    require_once 'Catalog.php';
                    $form = 'CatalogForm';
                    break;
                case 'gift':
                    require_once 'Gift.php';
                    $form = 'GiftForm';
                    break;
                case 'account':
                    require_once 'Account.php';
                    $form = 'AccountForm';
                    break;
                case 'detail':
                    require_once 'Detail.php';
                    $form = 'DetailForm';
                    break;
                case 'newaddress':
                    require_once 'NewAddress.php';
                    $form = 'NewAddressForm';
                    break;
                case 'receiver':
                    require_once 'Receiver.php';
                    $form = 'ReceiverForm';
                    break;
                case 'confirm':
                    require_once 'Confirm.php';
                    $form = 'ConfirmForm';
                    break;
                case 'viewbrand':
                    require_once 'ViewBrand.php';
                    $form = 'ViewBrandForm';
                    break;
                case 'officeloc':
                    require_once 'OfficeLoc.php';
                    $form = 'OfficeLocForm';
                    break;
//				case 'send':
//					require_once 'Send.php';
//					$form = 'SendForm';
//					break;
                case 'help':
                    require_once 'Help.php';
                    $form = 'HelpForm';
                    break;
                case 'resetpass':
                    require_once 'ResetPass.php';
                    $form = 'ResetPassForm';
                    break;
                case 'changepass':
                    require_once 'ChangePass.php';
                    $form = 'ChangePassForm';
                    break;
                case 'onsetuppin':
                    require_once 'SetupPin.php';
                    $form = 'SetupPinForm';
                    break;
                case 'onsetupphone':
                    require_once 'SetupPhone.php';
                    $form = 'SetupPhoneForm';
                    break;
                case 'login':
                    require_once 'LogIn.php';
                    $form = 'LogInForm';
                    break;
				case 'ulogin':
					require_once 'ULogIn.php';
					$form = 'ULogInForm';
					break;
                case 'notice':
                    require_once 'Notice.php';
                    $form = 'NoticeForm';
                    break;
				case 'sendsuccess':
					require_once 'SendSuccess.php';
					$form = 'SendSuccessForm';
					break;
				case 'topup':
					require_once 'Topup.php';
					$form = 'TopupForm';
					break;
				case 'expired':
					require_once dirname(__DIR__). '/Element/Expired.php';
					$form = 'ExpiredForm';
					break;
				case 'valid':
					require_once dirname(__DIR__). '/Element/ValidTime.php';
					$form = 'ValidTimeForm';
					break;
                case 'open_in_app':
                    require_once dirname(__DIR__). '/Element/OpenPopupApp.php';
                    $form = 'OpenPopupAppForm';
                    break;
                default:
                    require_once 'Home.php';
                    $form = 'HomeForm';
                    break;
            }

            Language::$activeLang = isset($card['lang']) ? $card['lang'] : 'vi';
            Language::loadWordsFromLang(Language::$activeLang);
            Language::cookie(true, Language::$activeLang);

            if ($card['survey_id'] > 0) {
                $survey = Balance::checkSurvey($token);
                if ($survey > 0) {
                    require_once 'Survey.php';
                    $form = 'SurveyForm';
                }
            }
        }
        Module::addForm(new $form());
    }
}