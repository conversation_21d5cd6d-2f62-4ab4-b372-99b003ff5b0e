<div class="page" data-name="setpin">
    {include file="../Element/header.tpl"}
    <div class="page-content page-content-scroll keep-navbar-on-scroll">
        <section class="login_by_phone">
            <div class="title_phone_login">
                Chào mừng bạn quay lại UrBox!
            </div>
            <form action="/ajax/card/auth/Login" id="form-login">
                <div class="container m-auto">
                    <div class="formx1">
                        <div class="cardX1">
                            {if !empty($app.image_welcome)}
                                <img src="{$app.image_welcome}" class="w100p" height="205" alt="">
                            {else}
                                <img src="/mobile/style/card/images/auth/cardframe2x.png" height="205"/>
                            {/if}
                            <div class="card-info">
                                <label>Gift Card</label>
                                <strong class="card-number">{chunk_split($card['number'], 4 ," ") }</strong>
                            </div>
                            <div class="copyCardNumber"><span>Sao chép</span></div>
                        </div>

                        <div class="container m-auto">
                            <div class="box-input-phone">
                                <div class="title_pin">Nhập mật khẩu của bạn</div>
                                <div class="m-auto">
                                    <div class="form_input_pin">
                                        <input type="password" maxlength="1" autocomplete="off" pattern="[0-9]*"
                                               oninput="inputInsideOtpInput(this,event)" inputmode="numeric"
                                               class="input-pin input-login scroll-top" id="input-pin">
                                        <input type="password" maxlength="1" autocomplete="off" pattern="[0-9]*"
                                               oninput="inputInsideOtpInput(this,event)" inputmode="numeric"
                                               class="input-pin input-login scroll-top focusToBackElement" id="k2">
                                        <input type="password" maxlength="1" autocomplete="off" pattern="[0-9]*"
                                               oninput="inputInsideOtpInput(this,event)" inputmode="numeric"
                                               class="input-pin input-login scroll-top focusToBackElement" id="k3">
                                        <input type="password" maxlength="1" autocomplete="off" pattern="[0-9]*"
                                               oninput="inputInsideOtpInput(this,event)" inputmode="numeric"
                                               class="input-pin input-login scroll-top focusToBackElement" id="k4">
                                        <input type="password" maxlength="1" autocomplete="off" pattern="[0-9]*"
                                               oninput="inputInsideOtpInput(this,event)" inputmode="numeric"
                                               class="input-pin input-login scroll-top focusToBackElement" id="k5">
                                        <input type="password" maxlength="1" autocomplete="off" pattern="[0-9]*"
                                               oninput="inputInsideOtpInput(this,event)" inputmode="numeric"
                                               class="input-pin input-login scroll-top focusToBackElement">
                                    </div>
                                    <input type="hidden" maxlength="7" id="password-input" name="password" value="">
                                    <div class="wrong_pass">
                                        Mật khẩu không chính xác
                                    </div>
                                </div>
                                <div class="signout justify-content-space-between">

                                    <div></div>

                                    <a href="javascript:void(0)"
                                       onclick="_.auth.logout()">Đăng xuất</a>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </form>
            <div class="downapp1_box ft_login">
                <span>Tải UrBox về điện thoại để được tổng hợp số dư <br> và thoải mái mua sắm</span>
                <div>
                    <a href="urbox://app/Home" onclick="_.onOpenClick()" class="external open-button"
                       id="open-button">
                        <img src="/mobile/style/card/images/auth/app-store-badge.svg"/>
                    </a>
                    <a href="urbox://app/Home" onclick="_.onOpenClick()" class="gg external open-button"
                       id="open-button">
                        <img src="/mobile/style/card/images/auth/google-play-badge.svg"/>
                    </a>
                </div>
            </div>

        </section>
    </div>
</div>
