<div class="page" data-name="otp">
    {include file='../Element/header.tpl'}
    <div class="page-content page-content-scroll keep-navbar-on-scroll">
        <section class="login_by_phone">
            <div class="title_phone_login">Nhập mã OTP</div>
            <form action="/ajax/card/auth/verifyOtp" id="otp-form">
                <div class="container m-auto">
                <div class="formx1">
                    <div class="cardX1">
                        {if !empty($app.image_welcome)}
                            <img src="{$app.image_welcome}" class="w100p" height="205" alt="">
                        {else}
                            <img src="/mobile/style/card/images/auth/cardframe2x.png" height="205"/>
                        {/if}
                        <div class="card-info">
                            <label>Gift Card</label>
                            <strong class="card-number">{chunk_split($card['number'], 4 ," ") }</strong>
                        </div>
                        <div class="copyCardNumber"><span>Sao chép</span></div>
                    </div>

                    <div class="container m-auto">
                        <div class="box-input-phone">
                            <div class="m-auto">
                                <div class="form_input_otp">
                                    <input type="number" maxlength="1" autocomplete="off" pattern="[0-9]*"
                                           oninput="inputInsideOtpInput(this,event)" inputmode="numeric"
                                           class="input-otp" id="setOtpInput"
                                           placeholder="0">
                                    <input type="number" maxlength="1" autocomplete="off" pattern="[0-9]*"
                                           oninput="inputInsideOtpInput(this,event)" inputmode="numeric"
                                           class="input-otp focusToBackElement"
                                           placeholder="0">
                                    <input type="number" maxlength="1" autocomplete="off" pattern="[0-9]*"
                                           oninput="inputInsideOtpInput(this,event)" inputmode="numeric"
                                           class="input-otp focusToBackElement"
                                           placeholder="0">
                                    <input type="number" maxlength="1" autocomplete="off" pattern="[0-9]*"
                                           oninput="inputInsideOtpInput(this,event)" inputmode="numeric"
                                           class="input-otp focusToBackElement"
                                           placeholder="0">
                                    <input type="number" maxlength="1" autocomplete="off" pattern="[0-9]*"
                                           oninput="inputInsideOtpInput(this,event)" inputmode="numeric"
                                           class="input-otp focusToBackElement"
                                           placeholder="0">
                                    <input type="number" maxlength="1" autocomplete="off" pattern="[0-9]*"
                                           oninput="inputInsideOtpInput(this,event)" inputmode="numeric"
                                           class="input-otp focusToBackElement"
                                           placeholder="0">
                                </div>
                                <div class="wrong_pass {if $message != '' } display-block {/if}">
                                    {if $message != ''}
                                        {{$message}}
                                    {else}
                                        OTP không chính xác
                                    {/if}
                                </div>
                            </div>
                            <div class="back-otp">
                                {if $request_otp != 1}
                                    <a href="javascript:void(0)" onclick="_.link('/card?back_action=1')">
                                        <i class="f7-icons">chevron_left</i>
                                        Quay lại</a>
                                {else}
                                    <a href="javascript:void(0)" onclick="_.link('/card?back_action=2')">
                                        <i class="f7-icons">chevron_left</i>
                                        Quay lại</a>
                                {/if}
                                <label>OTP đã được gửi (<span id="countdown">60s</span>)</label>
                                <label for="" class="resend-OTP"><a href="javascript:void(0)"
                                                                    onclick="_.resendOtp()">Gửi lại OTP</a></label>
                            </div>
                        </div>
                    </div>
                </div>
                </div>
            </form>
            <div class="downapp1_box ft_login">
                <span>Tải UrBox về điện thoại để được tổng hợp số dư<br>và thoải mái mua sắm</span>
                <div>
                    <a href="urbox://app/Home" onclick="_.onOpenClick('desktop_iOs')" class="external open-button"
                       id="open-button">
                        <img src="/mobile/style/card/images/auth/app-store-badge.svg"/>
                    </a>
                    <a href="urbox://app/Home" onclick="_.onOpenClick('desktop_Android')"
                       class="gg external open-button" id="open-button">
                        <img src="/mobile/style/card/images/auth/google-play-badge.svg"/>
                    </a>
                </div>
            </div>

        </section>
    </div>
</div>
