<?php

class RegisterForm extends Form
{
    function draw()
    {
        global $display;
        $ip = Card::clientIp();
        $display->add('ip', $ip);
        $requestNum = CookieLib::get('requestNum');
        if ($requestNum == "") {
            CookieLib::set('requestNum', 0);
        }
        $accessTime = CookieLib::get('accessTime');
        if ($accessTime == "") {
            CookieLib::set('accessTime', System::encrypt(TIME_NOW));
        }
        $brandByCat = Brand::brandByCat();
        $hotBrands = [];
        foreach ($brandByCat as $key => $brandList) {
            if(isset($brandList['brand'])) {
                $tpm = array_filter($brandList['brand'], function ($brand) {
                    return $brand['is_hot'] == 1;
                });
                $hotBrands = array_merge($hotBrands, $tpm);
            }
        }
        $display->add('brandByCat', $brandByCat);
        $display->add('hotBrands', $hotBrands);
        return $display->output("Auth/Register/Register");
    }
}