@extends('auth')

@section('content')
    <div class="page" data-name="loginExpired">
        <div class="hidden" id="title">
            UrBox :: {{trans('card.title.loginExpired')}}
        </div>
        @include('auth.header')
        <div class="page-content keep-navbar-on-scroll">
            <section class="balancelogin_box loginExpired">
                <div class="container m-auto card-expired">
                    <h2>{{trans('card.login.card_expired')}}</h2>
                    <div class="cap">
                        {{trans('card.login.card_price')}} <strong>{{ number_format($card['money'],0,',','.') }}
                            {VIEW_CURRENCY}</strong><br/>
                        {{trans('card.login.expiration')}} <b>{{$card['expired']}}</b>
                    </div>
                    <form method="">
                        <div class="formx1">
                            <div class="cardX1">
                                <img src="{{asset('images/auth/cardframe2x.png')}}"/>
                                <div class="card-info">
                                    <label>Gift Card</label>
                                    <strong class="card-number">{{ chunk_split($card['number'], 4 ," ") }}</strong>
                                </div>
                                <div class="copyCardNumber"><span>{{trans('card.copy')}}</span></div>
                            </div>
                            <div class="signout">
                                <label>{{trans('card.login.another_giftcard')}}</label>
                                <a href="javascript:void(0)"
                                   onclick="_.link('{{ url7('/logout') }}')">{{trans('card.login.signout')}}</a>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="ft">
                    <p>{{trans('card.call_the_hotline')}}</p>
                    <div class="call text-center">
                        <a href="{{\App\Constant\UrBox::TEL_CUSTOMER_NUMBER}}" class="external">
                            <strong>{{\App\Constant\UrBox::CUSTOMER_NUMBER}}</strong>
                        </a>
                    </div>
                </div>
            </section>
        </div>
@endsection
