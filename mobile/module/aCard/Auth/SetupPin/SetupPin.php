<?php

class SetuppinForm extends Form
{

    function draw()
    {
        global $display;
        $request_otp = SessionLib::get('request_otp');
        $display->add('request_otp',$request_otp);
        $display->add('message','');
        $app = System::$data['apps'];
        $display->add('app',$app);
        $card = System::$data['card'];
        $display->add('card',$card);
        return $display->output("Auth/SetupPin/SetupPin");
    }
}