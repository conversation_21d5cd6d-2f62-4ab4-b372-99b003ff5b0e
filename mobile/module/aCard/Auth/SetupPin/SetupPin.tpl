<div class="page" data-name="setpin">
    {include file='../Element/header.tpl'}

    <div class="page-content page-content-scroll keep-navbar-on-scroll">
        <section class="login_by_phone">
            <div class="title_phone_login">
                Đặt lại mật khẩu
            </div>
            <div class="container m-auto">
                <form action="/ajax/card/auth/SetupPin" id="form-setPin">
                    <div class="container m-auto">
                        <div class="formx1">
                            <div class="cardX1">
                                {if !empty($app.image_welcome)}
                                    <img src="{$app.image_welcome}" class="w100p" height="205" alt="">
                                {else}
                                    <img src="/mobile/style/card/images/auth/cardframe2x.png" height="205"/>
                                {/if}
                                <div class="card-info">
                                    <label>Gift Card</label>
                                    <strong class="card-number">{chunk_split($card['number'], 4 ," ") }</strong>
                                </div>
                                <div class="copyCardNumber"><span>Sao chép</span></div>
                            </div>

                            <div class="box-input-phone pT40 pB55" id="neo-block">
                                <div class="m-auto">
                                    <div class="legend">
                                        <div class="fieldset">Mật khẩu mới</div>
                                        <div class="form_input_pin">
                                            <input type="password" maxlength="1" autocomplete="off" pattern="[0-9]*"
                                                   oninput="inputInsideOtpInput(this,event)" inputmode="numeric"
                                                   class="input-pin pinA reset-pin" id="input-pin">
                                            <input type="password" maxlength="1" autocomplete="off" pattern="[0-9]*"
                                                   oninput="inputInsideOtpInput(this,event)" inputmode="numeric"
                                                   class="input-pin pinA reset-pin focusToBackElement">
                                            <input type="password" maxlength="1" autocomplete="off" pattern="[0-9]*"
                                                   oninput="inputInsideOtpInput(this,event)" inputmode="numeric"
                                                   class="input-pin pinA reset-pin focusToBackElement">
                                            <input type="password" maxlength="1" autocomplete="off" pattern="[0-9]*"
                                                   oninput="inputInsideOtpInput(this,event)" inputmode="numeric"
                                                   class="input-pin pinA reset-pin focusToBackElement">
                                            <input type="password" maxlength="1" autocomplete="off" pattern="[0-9]*"
                                                   oninput="inputInsideOtpInput(this,event)" inputmode="numeric"
                                                   class="input-pin pinA reset-pin focusToBackElement">
                                            <input type="password" maxlength="1" autocomplete="off" pattern="[0-9]*"
                                                   oninput="inputInsideOtpInput(this,event)" inputmode="numeric"
                                                   class="input-pin pinA reset-pin focusToBackElement">
                                        </div>
                                    </div>
                                    <div class="legend">
                                        <div class="fieldset">Xác nhận lại mật khẩu</div>
                                        <div class="form_input_pin">
                                            <input type="password" maxlength="1" autocomplete="off" pattern="[0-9]*"
                                                   oninput="inputInsideOtpInput(this,event)" inputmode="numeric"
                                                   class="input-pin pinB reset-pin">
                                            <input type="password" maxlength="1" autocomplete="off" pattern="[0-9]*"
                                                   oninput="inputInsideOtpInput(this,event)" inputmode="numeric"
                                                   class="input-pin pinB reset-pin focusToBackElement">
                                            <input type="password" maxlength="1" autocomplete="off" pattern="[0-9]*"
                                                   oninput="inputInsideOtpInput(this,event)" inputmode="numeric"
                                                   class="input-pin pinB reset-pin focusToBackElement">
                                            <input type="password" maxlength="1" autocomplete="off" pattern="[0-9]*"
                                                   oninput="inputInsideOtpInput(this,event)" inputmode="numeric"
                                                   class="input-pin pinB reset-pin focusToBackElement">
                                            <input type="password" maxlength="1" autocomplete="off" pattern="[0-9]*"
                                                   oninput="inputInsideOtpInput(this,event)" inputmode="numeric"
                                                   class="input-pin pinB reset-pin focusToBackElement">
                                            <input type="password" maxlength="1" autocomplete="off" pattern="[0-9]*"
                                                   oninput="inputInsideOtpInput(this,event)" inputmode="numeric"
                                                   class="input-pin pinB reset-pin focusToBackElement">
                                        </div>
                                    </div>
                                    <div class="wrong_pass">
                                        Mật khẩu không đồng nhất, vui lòng nhập lại
                                    </div>
                                    <div class="button-login" onclick="_.auth.setPin()">
                                        <a href="javascript:void(0)">
                                            <img src="/mobile/style/card/images/auth/bt_normal.svg" alt=""
                                                 class="img-login-phone">
                                            <img src="/mobile/style/card/images/auth/Bt_Active.svg" alt=""
                                                 class="img-active-phone hidden">
                                        </a>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="downapp1_box ft_login ft-mod">
                <span>Tải UrBox về điện thoại để được tổng hợp số dư <br> và thoải mái mua sắm</span>
                <div>
                    <a href="urbox://app/Home" onclick="_.onOpenClick()" class="external open-button" id="open-button">
                        <img src="/mobile/style/card/images/auth/app-store-badge.svg"/>
                    </a>
                    <a href="urbox://app/Home" onclick="_.onOpenClick()" class="gg external open-button"
                       id="open-button">
                        <img src="/mobile/style/card/images/auth/google-play-badge.svg"/>
                    </a>
                </div>
            </div>

        </section>
    </div>
</div>

