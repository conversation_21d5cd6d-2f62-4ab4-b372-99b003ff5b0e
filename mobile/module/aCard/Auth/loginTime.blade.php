@extends('auth')

@section('content')
    <div class="page" data-name="loginTime">
        <div class="hidden" id="title">
            UrBox :: {{trans('card.title.loginTime')}}
        </div>
        @include('auth.header')
        <div class="page-content keep-navbar-on-scroll">
            <section class="balancelogin_box loginTime">
                <div class="container m-auto">
                    <h2>{{trans('card.login.Congratulations')}}</h2>
                    <div class="cap">
                        {{trans('card.login.got_new_giftcard')}}
                        <strong>{{ number_format($card['money'],0,',','.') }}{VIEW_CURRENCY}</strong><br/>
                        {{trans('card.login.use_balance_to_redeem')}}
                    </div>
                    <form method="">
                        <div class="formx1">
                            <div class="cardX1">
                                <img src="{{asset('images/auth/cardframe2x.png')}}"/>
                                <div class="card-info">
                                    <label>Gift Card</label>
                                    <strong class="card-number">{{ chunk_split($card['number'], 4 ," ") }}</strong>
                                </div>
                                <div class="copyCardNumber"><span>{{trans('card.copy')}}</span></div>
                            </div>
                            <div class="cap card-not-expired">
                                <span class="f-500">{{trans('card.login.not_expired')}}</span> <br>
                                <div><b>{{$card['valid_time']}}</b> @if($card['expired']) {{trans('card.login.to')}}
                                    <b>{{$card['expired']}}</b> @endif </div>
                                {{trans('card.login.comeback')}}
                            </div>
                            <div class="signout">
                                <label>{{trans('card.login.another_giftcard')}}</label>
                                <a href="javascript:void(0)"
                                   onclick="_.link('{{ url7('/logout') }}')">{{trans('card.login.signout')}}</a>
                            </div>
                        </div>
                    </form>

                </div>
                <div class="ft">
                    <p>{{trans('card.call_the_hotline')}}</p>
                    <div class="call text-center">
                        <a href="{{\App\Constant\UrBox::TEL_CUSTOMER_NUMBER}}" class="external">
                            <strong>{{\App\Constant\UrBox::CUSTOMER_NUMBER}}</strong>
                        </a>
                    </div>
                </div>
            </section>
        </div>
@endsection
