<?php

class SuccessForm extends Form
{
    public function __construct()
    {
    }

    public function draw()
    {
        global $display;
        $encode = CookieLib::get(md5('vinid'));
        if ($encode == ""){
            Url::redirect("vinid");
        }
        $token = !empty(System::$data['url']['query'][1]) ? System::$data['url']['query'][1] : '';
        $data = System::decode($encode);
        if ($data == null){
            CookieLib::set(md5('vinid'),"",TIME_NOW);
            Url::redirect("vinid");
        }
        $display->add("token",$token);
        return $display->output('success');
    }
}