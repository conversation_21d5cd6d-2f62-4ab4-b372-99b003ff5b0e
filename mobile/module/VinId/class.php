<?php

class VinId extends Module
{
    function __construct($block)
    {
        Module::initialize($block);
        Form::addCss('mobile/libs/_f7/css/framework7.bundle.min.css');
        Form::addCss('mobile/libs/_f7/plugins/keyboard/css/framework7.keypad.css');
        Form::addCss('mobile/style/vinid/default.css');
        Form::addCss('mobile/style/vinid/style.css');
        Form::addJs('mobile/libs/_f7/js/framework7.bundle.js');
        Form::addJs('mobile/libs/_f7/plugins/keyboard/js/framework7.keypad.min.js');
        Form::addJs('mobile/javascript/vinid/routes.js');
        Form::addJs('mobile/javascript/vinid/app.js');
        $token = !empty(System::$data['url']['query'][1]) ? System::$data['url']['query'][1] : '';
		$page = Url::getParam('page',"home");
        if ($token == ""){
            Url::redirect("home");
        }
        $data = System::decode($token);
        if ($data == null){
            Url::redirect("home");
        }

        switch ($page) {
            case 'topup':
                require_once 'topup.php';
                $form = 'TopUpForm';
                break;
            case 'success':
                require_once 'success.php';
                $form = 'SuccessForm';
                break;
            case 'error':
                require_once 'error.php';
                $form = 'ErrorForm';
                break;
            default:
                require_once 'home.php';
                $form = 'HomeForm';
                break;
        }
        Module::addForm(new $form());
    }
}
