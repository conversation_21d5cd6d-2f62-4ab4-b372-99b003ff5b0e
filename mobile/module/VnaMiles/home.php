<?php

class HomeForm extends Form
{
    public function __construct()
    {
    }

    public function draw()
    {
        global $display;
		$card = System::$data['card'];
		$customer = Customer::get($card['customer_id']);
		$app = System::$data['app'];
		$display->add("app",$app);
		$display->add("card",$card);
		$display->add("customer",$customer);
		$display->add('cur_lang', Language::$activeLang);
		$display->add('langs', Language::$listLangOptions);
		CookieLib::set('error_msg',"", TIME_NOW);
        return $display->output('home');
    }
}