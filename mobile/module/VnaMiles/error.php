<?php

class ErrorForm extends Form
{
    public function __construct()
    {
    }

    public function draw()
    {
		global $display;
		$card = System::$data['card'];
	
		if(empty($card)){
			$token = System::$data['token'];
		}else{
			$token = $card['token'];
		}
		unset($_SESSION['vna_authen']);
		VnaMile::delAuth($token);
		$error = "Xin lỗi quý khách, hệ thống đang tạm mất kết nối. Quý khách vui lòng thử lại sau";
		$display->add('error',$error);
		$display->add('token',$token);
		return $display->output('error');
    }
}