<div class="page" data-name="topup">


    <div class="page-content no-padding bg-color">
        <div class="navbar header no-bg">
            <div class="vne-container">
                <div class="header-content">
                    <div id="menu-language" class="menu-item menu-item-dropdown no-bg">
                        <div class="menu-item-content">
                            <div class="menu-button-dropdown">
                                {$cur_lang|strtoupper}
                                <span class="icon-arrow">
                                <i class="fa fa-caret-down" aria-hidden="true"></i>
                            </span>
                            </div>
                        </div>
                        <div class="menu-dropdown menu-dropdown-center">
                            <div class="menu-dropdown-content">
                                {foreach from=$langs item=item key=k name=i}
                                    <div class="menu-dropdown-item" {if $k != $cur_lang} onclick="_.mod.changeLanguage('{$k}')"{/if} >
                                        <label class="input-radio-container">{$k|strtoupper}
                                            <input type="radio" {if $k == $cur_lang}checked="checked"{/if} name="radio">
                                            <span class="check-mark"></span>
                                        </label>
                                    </div>

                                {/foreach}

                            </div>
                        </div>
                    </div>
                    <div class="logo">
                        <div class="logo-content">
                            <a href="javascript:;">
                                <img src="{WEB_ROOT}mobile/style/vnaMiles/images/logo-color.svg" alt="">
                            </a>
                        </div>
                    </div>
                    {if $app.description != ""}
                    <div class="rules">
                        <div class="rules-content">
                            <a href="javascript:_.app.popup.open('#popup-term');">
                                <img src="{WEB_ROOT}mobile/style/vnaMiles/images/menu-term.svg" alt="" style="vertical-align: middle;"> {"Thể lệ"|t}
                            </a>
                        </div>
                    </div>
                    {/if}
                </div>
            </div>
        </div>
        {if $app.description != ""}
        <div id="popup-term" class="popup popup-term">
            <div class="popup-header">
                <a class="fl mL10" href="javascript:_.app.popup.close('#popup-term');"><img src="{WEB_ROOT}mobile/style/vnaMiles/images/arrow-back.svg" alt=""></a>
                <h3>{"Thể lệ chương trình"|t}</h3>
            </div>
            <div class="block color-white">
                {$app.description}
            </div>
            <div class="form-footer">
                <div class="footer-content">
                    <div class="copy-right">
                        <img src="{WEB_ROOT}mobile/style/vnaMiles/images/footer.svg" alt="">
                    </div>
                    <div class="footer-bar">
                        <div class="footer-bar-content"></div>
                    </div>
                </div>
            </div>
        </div>
        {/if}
        <div class="banner">
            <div class="vne-container">
                <div class="banner-content">
                    <div class="base-image">
                        <div class="base-planes">
                            <img src="{WEB_ROOT}mobile/style/vnaMiles/images/planes.png" alt="">
                        </div>
                    </div>
                    <div class="animation-image">
                        <div class="animation-image-step step-1">
                            <img src="{WEB_ROOT}mobile/style/vnaMiles/images/cloud3.png" alt="">
                        </div>
                        <div class="animation-image-step step-2">
                            <img src="{WEB_ROOT}mobile/style/vnaMiles/images/cloud3.png" alt="">
                        </div>
                        <div class="animation-image-step step-3">
                            <img src="{WEB_ROOT}mobile/style/vnaMiles/images/cloud4.png" alt="">
                        </div>
                        <div class="animation-image-step step-4">
                            <img src="{WEB_ROOT}mobile/style/vnaMiles/images/cloud5.png" alt="">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="form-input">
            <div class="vne-container" style="height: 100%">
                <div class="form-input-content">
                    <div class="form-header">
                        <span>{"Chương trình đồng hành cùng"|t}: </span>&nbsp;<strong>{$app.title}</strong>
                    </div>
                    <div class="form-main">
                        <div class="form-main-content">
                            <div class="form-main-text">
                                <span id="message-content">{"Quý khách vui lòng điền chính xác thông tin tài khoản để nạp dặm bay Vietnam Airlines"|t}</span>
                            </div>
                            {if $card['expired'] != 0}
                                <div class="form-main-text" style="width: 100%; margin: 5px auto; text-align:center">
                                    <b>{'Hạn sử dụng'|t}: <span class="color-vna">{date('d/m/Y',$card['expired'])}</span></b>
                                </div>
                            {/if}
                            <div class="input-form" style="padding:15px 10px;">
                                <div class="input-wrapper pR50">
                                    <div class="refill-info">
                                        <div class="refill-info-box">
                                            <span>{"Giá trị dặm đang nạp"|t}</span>
                                            <strong>{$card.amount|number_format:0:",":"."}</strong>
                                        </div>
                                        <div class="refill-info-box">
                                            <span>{"Loại dặm"|t}</span>
                                            <strong>{"Dặm thưởng"|t}</strong>
                                        </div>
                                    </div>
                                    <div class="material-input-form">
                                        <div class="form-group">
                                            <label for="memberId">{"Mã hội viên"|t}</label>
                                            <input type="text" id="memberId" class="form-control form-control-material press-key f-600" maxlength="11" placeholder="00000 00000">
                                        </div>
                                        <div class="form-group">
                                            <label for="memberFirstName">{"Tên"|t} ({"không dấu"|t})</label>
                                            <input type="text" id="memberFirstName" class="form-control form-control-material press-key f-600" maxlength="25" placeholder="VAN A">
                                        </div>
                                        <div class="form-group">
                                            <label for="memberSurName">{"Họ"|t}({"không dấu"|t})</label>
                                            <input type="text" id="memberSurName" class="form-control form-control-material press-key f-600" maxlength="20" placeholder="Nguyen">
                                        </div>

                                        <div class="form-group">
                                            <label for="memberPhone">{"Số điện thoại"|t}</label>
                                            <input type="text" id="memberPhone" class="form-control form-control-material press-key f-600" maxlength="10" placeholder="0912 345 678">
                                        </div>

                                        <div class="form-group">
                                            <label for="memberEmail">{"Email"|t} <span class="color-vna">({"không bắt buộc"|t})</span></label>
                                            <input type="text" maxlength="100" id="memberEmail" class="form-control form-control-material press-key f-600" placeholder="<EMAIL>">
                                        </div>

                                    </div>
                                </div>
                                <div class="form-inner-right"></div>
                            </div>
                            <div class="color-white mT20 text-align-left">
                                <label class="checkbox"><input type="checkbox" class="confirmOK" name="confirmOK" id="confirmOk"><i class="icon-checkbox"></i></label>
                                {'Tôi đồng ý cung cấp thông tin phục vụ cho việc nạp dặm vào Tài khoản Bông Sen Vàng'|t}
                            </div>
                            <div class="button-form">
                                <button class="btn-disabled" id="btn-topup" onclick="_.mod.topup('{$card.token}')">{"Nạp dặm"|t}</button>
                                <input type="hidden" id="hidden-token" value="{$card.token}">
                            </div>
                        </div>
                    </div>
                    <div class="form-footer">
                        <div class="footer-content">
                            <div class="copy-right">
                                <img src="{WEB_ROOT}mobile/style/vnaMiles/images/footer.svg" alt="">
                            </div>
                            <div class="footer-bar">
                                <div class="footer-bar-content"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
