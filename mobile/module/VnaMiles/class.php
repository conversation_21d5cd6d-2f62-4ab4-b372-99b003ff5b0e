<?php

class VnaMiles extends Module
{
    function __construct($block)
    {
        Module::initialize($block);
		Form::addCss('mobile/libs/_f7.4.5.10/css/framework7.bundle.min.css');
		Form::addJs('mobile/libs/_f7.4.5.10/js/framework7.bundle.js');
        Form::addCss('mobile/style/default.css');
        Form::addCss('mobile/style/vnaMiles/style.css');
        Form::addCss('mobile/style/vnaMiles/banner.css');
        Form::addCss('mobile/style/vnaMiles/input-pin.css');
        Form::addJs('mobile/javascript/vnaMiles/routes.js');
        Form::addJs('mobile/javascript/vnaMiles/app.js');
		$page = Url::getParam('page','home');
	
		$app = !empty(System::$data['url']['query'][1]) ? System::$data['url']['query'][1] : '';
		$app = preg_replace('/[^A-Za-z0-9\-]/', '', $app);
		$app = trim($app);
	
		$token = !empty(System::$data['url']['query'][2]) ? System::$data['url']['query'][2] : '';
		$token = preg_replace('/[^A-Za-z0-9\-]/', '', $token);
		$token = trim($token);
		if($token == "" && $page != "error"){
			Url::redirect("vna/miles?page=error");
		}
		System::$data['token'] = $token;
		$card = Point::getByToken($token);
		if(empty($card) && !in_array($page,["error","lock"])){
			Url::redirect("vna/miles/{$token}?page=error");
		}
		if(!empty($card) && $card['process'] > 2 && $page != "success"){
			Url::redirect("vna/miles/{$card['token']}?page=success");
		}
		if((isset($card['lock_time'])) && $card['lock_time'] > TIME_NOW - 3600  && $page != "lock"){
			Url::redirect("vna/miles/{$token}?page=lock");
		}
	
		System::$data['card'] = $card;
		if(!in_array($page,["error","lock"])) {
			System::$data['app'] = Point::getApp( $card['app_id'] );
		}
		if((!isset(System::$data['app']) || empty(System::$data['app']) || System::$data['app']['status'] == 1) && !in_array($page,["error","lock"])){
			if($page != "error") Url::redirect("vna/miles/{$token}?page=error");
		}
        switch ($page) {
            case 'topup':
                require_once 'topup.php';
                $form = 'TopUpForm';
                break;
            case 'success':
                require_once 'success.php';
                $form = 'SuccessForm';
                break;
            case 'lock':
                require_once 'lock.php';
                $form = 'LockForm';
                break;
            case 'error':
                require_once 'error.php';
                $form = 'ErrorForm';
                break;
            default:
                require_once 'topup.php';
                $form = 'TopUpForm';
                break;
        }
        Module::addForm(new $form());
    }
}
