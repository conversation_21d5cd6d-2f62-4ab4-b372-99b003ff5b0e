<?php

class SuccessForm extends Form
{
    public function __construct()
    {
    }

    public function draw()
    {
        global $display;
		$card = System::$data['card'];
		if(empty($card)){
			$token = System::$data['token'];
		}else{
			$token = $card['token'];
		}
		unset($_SESSION['vna_authen']);
		VnaMile::delAuth($token);
		$customer = Customer::get($card['receiver_id']);
		$display->add("card",$card);
		$display->add("customer",$customer);
		return $display->output('success');
    }
}